# Frequently Asked Questions (FAQ)

## General Questions

### Q: What platforms are supported for video downloads?
A: Currently, the system supports:
- **B<PERSON>bili** (bilibili.com, b23.tv)
- **Douyin** (douyin.com, v.douyin.com, iesdouyin.com)

Support for additional platforms can be added by extending the downloader system.

### Q: Do I need an OpenAI API key?
A: Yes, an OpenAI API key is required for:
- GPT-based summarization (always required)
- Whisper API transcription (optional, you can use local Whisper instead)

You can get an API key from [OpenAI's website](https://platform.openai.com/api-keys).

### Q: Can I use the system without internet?
A: Partially. You can:
- Use local Whisper models for transcription (after initial download)
- Process local video/audio files
- However, you still need internet for:
  - Video downloads
  - GPT API summarization
  - Initial Whisper model downloads

## Installation Issues

### Q: "FFmpeg not found" error
A: FFmpeg is required for audio processing. Install it:

**Windows:**
1. Download from https://ffmpeg.org/download.html
2. Extract and add to system PATH
3. Restart command prompt

**macOS:**
```bash
brew install ffmpeg
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg
```

### Q: Python version compatibility issues
A: The system requires Python 3.8 or higher. Check your version:
```bash
python --version
```

If you have multiple Python versions, use:
```bash
python3.8 main.py  # or python3.9, python3.10, etc.
```

### Q: "No module named 'src'" error
A: Make sure you're running the script from the project root directory:
```bash
cd /path/to/tik
python main.py process "video_url"
```

## Configuration Issues

### Q: How do I set up my OpenAI API key?
A: You can set it in multiple ways:

**Method 1: Configuration file**
```yaml
# config/config.yaml
openai:
  api_key: "sk-your-api-key-here"
```

**Method 2: Environment variable**
```bash
export OPENAI_API_KEY="sk-your-api-key-here"
python main.py process "video_url"
```

**Method 3: .env file**
```bash
# .env
OPENAI_API_KEY=sk-your-api-key-here
```

### Q: "Configuration file not found" error
A: Create the configuration file:
```bash
cp config/config.example.yaml config/config.yaml
# Then edit config/config.yaml with your settings
```

### Q: How do I use different Whisper models?
A: Configure in `config/config.yaml`:
```yaml
whisper:
  use_local: true
  model_size: "large"  # tiny, base, small, medium, large, large-v2, large-v3
```

Or use command line:
```bash
python main.py process "video_url" --whisper-model large
```

## Usage Issues

### Q: Video download fails with "Unsupported platform"
A: Check if the URL is from a supported platform (Bilibili or Douyin). Ensure the URL is:
- Complete and properly formatted
- Accessible (not private or region-restricted)
- From a supported platform

### Q: Transcription is inaccurate
A: Try these solutions:
1. **Use a larger Whisper model:**
   ```bash
   python main.py process "video_url" --whisper-model large
   ```

2. **Specify the language:**
   ```yaml
   # config/config.yaml
   whisper:
     language: "zh"  # for Chinese
   ```

3. **Check audio quality** - poor audio leads to poor transcription

4. **Use OpenAI API** instead of local model:
   ```bash
   python main.py process "video_url" --no-whisper-local
   ```

### Q: Summarization results are poor
A: Improve summarization by:

1. **Using custom prompts:**
   ```bash
   python main.py process "video_url" --custom-prompt "Summarize the key technical points in bullet format"
   ```

2. **Trying different summary types:**
   ```bash
   python main.py process "video_url" --summary-type detailed
   ```

3. **Using a better GPT model:**
   ```yaml
   # config/config.yaml
   openai:
     model: "gpt-4"  # instead of gpt-3.5-turbo
   ```

### Q: "Out of memory" errors with Whisper
A: Solutions:
1. **Use smaller models:**
   ```bash
   python main.py process "video_url" --whisper-model tiny
   ```

2. **Use API instead of local:**
   ```bash
   python main.py process "video_url" --no-whisper-local
   ```

3. **Split long audio files** into smaller chunks

4. **Close other applications** to free up RAM

## API and Cost Issues

### Q: How much does it cost to process a video?
A: Costs depend on:
- **Whisper API:** ~$0.006 per minute of audio
- **GPT API:** Varies by model and text length
  - GPT-3.5-turbo: ~$0.002 per 1K tokens
  - GPT-4: ~$0.03 per 1K tokens

Estimate costs with:
```bash
python main.py info  # Shows current model settings
```

### Q: "API quota exceeded" error
A: This means you've hit OpenAI's rate limits:
1. **Wait** for the quota to reset
2. **Upgrade** your OpenAI plan
3. **Use shorter text** for summarization
4. **Process fewer videos** simultaneously

### Q: "Invalid API key" error
A: Check that:
1. API key is correct (starts with "sk-")
2. API key hasn't expired
3. You have sufficient credits
4. API key has required permissions

## Performance Issues

### Q: Processing is very slow
A: Speed up processing:

1. **Use GPU acceleration** (for local Whisper):
   ```bash
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

2. **Use smaller Whisper models:**
   ```bash
   python main.py process "video_url" --whisper-model base
   ```

3. **Use API services** instead of local processing:
   ```bash
   python main.py process "video_url" --no-whisper-local
   ```

4. **Process shorter videos** or split long videos

### Q: High memory usage
A: Reduce memory usage:
1. Use smaller Whisper models
2. Use API services instead of local models
3. Process videos individually instead of batch processing
4. Close other applications

## File and Directory Issues

### Q: "Permission denied" errors
A: Fix permission issues:
1. **Run with administrator privileges** (Windows) or `sudo` (Linux/macOS)
2. **Check directory permissions:**
   ```bash
   chmod 755 downloads transcriptions summaries
   ```
3. **Change output directory** to a location you have write access to

### Q: Output files not found
A: Check the output directory structure:
```
output/
├── downloads/          # Downloaded videos
├── transcriptions/     # Transcription files
└── summaries/          # Generated summaries
```

Use `--output-dir` to specify a custom location:
```bash
python main.py process "video_url" --output-dir "./my_project"
```

### Q: Disk space issues
A: Manage disk space:
1. **Don't keep videos** after processing:
   ```bash
   python main.py process "video_url" --no-keep-video
   ```
2. **Clean up old files** regularly
3. **Use external storage** for large video collections

## Advanced Usage

### Q: How do I process multiple videos?
A: Run the command multiple times:
```bash
python main.py process "video_url_1"
python main.py process "video_url_2"
python main.py process "video_url_3"
```

Or use a script:
```bash
#!/bin/bash
urls=(
    "https://www.bilibili.com/video/BV1111111111"
    "https://www.bilibili.com/video/BV2222222222"
    "https://v.douyin.com/abc123"
)

for url in "${urls[@]}"; do
    python main.py process "$url"
done
```

### Q: How do I customize the processing pipeline?
A: Use individual commands for more control:
```bash
# Step 1: Download only
python main.py download "video_url" --output-dir "./temp"

# Step 2: Transcribe with specific settings
python main.py transcribe "./temp/audio.wav" --local --model large

# Step 3: Summarize with custom prompt
python main.py summarize "./temp/transcript.txt" --type detailed
```

### Q: Can I use custom prompts for different types of content?
A: Yes, define custom prompts in `config/config.yaml`:
```yaml
summarization:
  prompts:
    technical: "Focus on technical details and provide step-by-step explanations"
    educational: "Structure as educational content with clear learning objectives"
    news: "Summarize the key facts and important developments"
```

Then use them:
```bash
python main.py process "video_url" --custom-prompt technical
```

## Troubleshooting

### Q: How do I enable debug logging?
A: Use verbose mode:
```bash
python main.py -v process "video_url"
```

Or set log level in configuration:
```yaml
logging:
  level: "DEBUG"
```

### Q: Where are the log files?
A: Check `logs/app.log` for detailed logs, or the location specified in your configuration.

### Q: How do I report bugs?
A: When reporting issues, include:
1. **Error message** (full traceback if available)
2. **Command used** and configuration
3. **System information** (OS, Python version)
4. **Log files** (with sensitive information removed)
5. **Steps to reproduce** the issue

Run with verbose logging to get more details:
```bash
python main.py -v info
python main.py -v process "video_url"
```
