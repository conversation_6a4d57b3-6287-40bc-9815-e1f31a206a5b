"""Download manager for coordinating different downloaders."""

from typing import List, Optional
from loguru import logger

from .base import BaseDownloader, DownloadResult
from .yt_dlp_downloader import YtDlpDownloader


class DownloadManager:
    """Manager for coordinating video downloads across different platforms."""
    
    def __init__(self, output_dir: str = "./downloads"):
        """Initialize download manager.
        
        Args:
            output_dir: Directory to save downloaded videos
        """
        self.output_dir = output_dir
        self.downloaders: List[BaseDownloader] = []
        
        # Register default downloaders
        self._register_default_downloaders()
    
    def _register_default_downloaders(self):
        """Register default downloaders."""
        # yt-dlp downloader for most platforms
        yt_dlp_downloader = YtDlpDownloader(self.output_dir)
        self.add_downloader(yt_dlp_downloader)
    
    def add_downloader(self, downloader: BaseDownloader):
        """Add a downloader to the manager.
        
        Args:
            downloader: Downloader instance to add
        """
        self.downloaders.append(downloader)
        logger.info(f"Added downloader: {downloader.__class__.__name__}")
    
    def get_compatible_downloader(self, url: str) -> Optional[BaseDownloader]:
        """Find a compatible downloader for the given URL.
        
        Args:
            url: Video URL
            
        Returns:
            Compatible downloader or None if not found
        """
        for downloader in self.downloaders:
            if downloader.can_handle(url):
                logger.info(f"Found compatible downloader: {downloader.__class__.__name__}")
                return downloader
        
        logger.warning(f"No compatible downloader found for URL: {url}")
        return None
    
    def download(self, url: str, extract_audio: bool = True) -> DownloadResult:
        """Download video using the appropriate downloader.
        
        Args:
            url: Video URL to download
            extract_audio: Whether to extract audio for transcription
            
        Returns:
            Download result
        """
        downloader = self.get_compatible_downloader(url)
        
        if not downloader:
            return DownloadResult(
                success=False,
                error_message=f"No compatible downloader found for URL: {url}",
            )
        
        try:
            logger.info(f"Starting download: {url}")
            result = downloader.download(url, extract_audio)
            
            if result.success:
                logger.info(f"Download completed successfully: {result.video_info.title if result.video_info else 'Unknown'}")
            else:
                logger.error(f"Download failed: {result.error_message}")
            
            return result
            
        except Exception as e:
            error_msg = f"Download failed with exception: {e}"
            logger.error(error_msg)
            return DownloadResult(
                success=False,
                error_message=error_msg,
            )
    
    def get_video_info(self, url: str):
        """Get video information without downloading.
        
        Args:
            url: Video URL
            
        Returns:
            Video information
            
        Raises:
            Exception: If no compatible downloader found or extraction fails
        """
        downloader = self.get_compatible_downloader(url)
        
        if not downloader:
            raise Exception(f"No compatible downloader found for URL: {url}")
        
        return downloader.get_video_info(url)
    
    def is_supported(self, url: str) -> bool:
        """Check if the URL is supported by any downloader.
        
        Args:
            url: Video URL to check
            
        Returns:
            True if supported, False otherwise
        """
        return self.get_compatible_downloader(url) is not None
