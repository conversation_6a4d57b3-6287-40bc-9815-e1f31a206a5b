"""Validation utilities for the video processing system."""

import re
from pathlib import Path
from typing import Optional
from urllib.parse import urlparse


def validate_url(url: str) -> bool:
    """Validate if a string is a valid URL.
    
    Args:
        url: URL string to validate
        
    Returns:
        True if valid URL, False otherwise
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_video_url(url: str) -> bool:
    """Validate if URL is from a supported video platform.
    
    Args:
        url: Video URL to validate
        
    Returns:
        True if supported platform, False otherwise
    """
    if not validate_url(url):
        return False
    
    # Supported platform patterns
    patterns = [
        # Bilibili
        r'https?://(?:www\.)?bilibili\.com/video/[^/]+',
        r'https?://(?:www\.)?bilibili\.com/bangumi/play/[^/]+',
        r'https?://b23\.tv/[^/]+',
        
        # Douyin
        r'https?://(?:www\.)?douyin\.com/video/[^/]+',
        r'https?://v\.douyin\.com/[^/]+',
        r'https?://(?:www\.)?iesdouyin\.com/share/video/[^/]+',
    ]
    
    for pattern in patterns:
        if re.match(pattern, url):
            return True
    
    return False


def validate_file_path(path: str, must_exist: bool = True) -> bool:
    """Validate file path.
    
    Args:
        path: File path to validate
        must_exist: Whether file must exist
        
    Returns:
        True if valid, False otherwise
    """
    try:
        file_path = Path(path)
        
        if must_exist:
            return file_path.exists() and file_path.is_file()
        else:
            # Check if parent directory exists or can be created
            return file_path.parent.exists() or file_path.parent.parent.exists()
    except Exception:
        return False


def validate_audio_file(path: str) -> bool:
    """Validate audio file.
    
    Args:
        path: Audio file path
        
    Returns:
        True if valid audio file, False otherwise
    """
    if not validate_file_path(path, must_exist=True):
        return False
    
    file_path = Path(path)
    audio_extensions = {'.wav', '.mp3', '.m4a', '.flac', '.ogg', '.aac', '.wma'}
    
    return file_path.suffix.lower() in audio_extensions


def validate_video_file(path: str) -> bool:
    """Validate video file.
    
    Args:
        path: Video file path
        
    Returns:
        True if valid video file, False otherwise
    """
    if not validate_file_path(path, must_exist=True):
        return False
    
    file_path = Path(path)
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
    
    return file_path.suffix.lower() in video_extensions


def validate_api_key(api_key: str) -> bool:
    """Validate OpenAI API key format.
    
    Args:
        api_key: API key to validate
        
    Returns:
        True if valid format, False otherwise
    """
    if not api_key or not isinstance(api_key, str):
        return False
    
    # OpenAI API keys start with 'sk-' and are typically 51 characters long
    return api_key.startswith('sk-') and len(api_key) >= 20


def validate_whisper_model(model: str) -> bool:
    """Validate Whisper model name.
    
    Args:
        model: Model name to validate
        
    Returns:
        True if valid model, False otherwise
    """
    valid_models = {
        'tiny', 'base', 'small', 'medium', 'large', 'large-v2', 'large-v3'
    }
    return model in valid_models


def validate_gpt_model(model: str) -> bool:
    """Validate GPT model name.
    
    Args:
        model: Model name to validate
        
    Returns:
        True if valid model, False otherwise
    """
    valid_models = {
        'gpt-3.5-turbo', 'gpt-3.5-turbo-16k',
        'gpt-4', 'gpt-4-turbo', 'gpt-4-turbo-preview',
        'gpt-4-32k'
    }
    return model in valid_models


def sanitize_filename(filename: str, max_length: int = 200) -> str:
    """Sanitize filename for filesystem compatibility.
    
    Args:
        filename: Original filename
        max_length: Maximum filename length
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove control characters
    filename = ''.join(char for char in filename if ord(char) >= 32)
    
    # Limit length
    if len(filename) > max_length:
        name, ext = Path(filename).stem, Path(filename).suffix
        max_name_length = max_length - len(ext)
        filename = name[:max_name_length] + ext
    
    return filename.strip()


def validate_output_directory(path: str, create: bool = True) -> bool:
    """Validate and optionally create output directory.
    
    Args:
        path: Directory path
        create: Whether to create directory if it doesn't exist
        
    Returns:
        True if valid/created, False otherwise
    """
    try:
        dir_path = Path(path)
        
        if dir_path.exists():
            return dir_path.is_dir()
        elif create:
            dir_path.mkdir(parents=True, exist_ok=True)
            return True
        else:
            return False
    except Exception:
        return False
