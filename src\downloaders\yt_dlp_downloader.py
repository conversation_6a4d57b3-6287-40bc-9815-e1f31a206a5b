"""yt-dlp based video downloader for Bilibili and Douyin."""

import re
import json
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, List
from loguru import logger

import yt_dlp
from yt_dlp.utils import DownloadError

from .base import BaseDownloader, VideoInfo, DownloadResult, Platform


class YtDlpDownloader(BaseDownloader):
    """Video downloader using yt-dlp library."""
    
    def __init__(self, output_dir: str = "./downloads", cookies_file: Optional[str] = None):
        """Initialize yt-dlp downloader.
        
        Args:
            output_dir: Directory to save downloaded videos
            cookies_file: Path to cookies file for authentication
        """
        super().__init__(output_dir)
        self.cookies_file = cookies_file
        
        # Platform URL patterns
        self.platform_patterns = {
            Platform.BILIBILI: [
                r'https?://(?:www\.)?bilibili\.com/video/[^/]+',
                r'https?://(?:www\.)?bilibili\.com/bangumi/play/[^/]+',
                r'https?://b23\.tv/[^/]+',
            ],
            Platform.DOUYIN: [
                r'https?://(?:www\.)?douyin\.com/video/[^/]+',
                r'https?://v\.douyin\.com/[^/]+',
                r'https?://(?:www\.)?iesdouyin\.com/share/video/[^/]+',
            ],
        }
    
    def can_handle(self, url: str) -> bool:
        """Check if this downloader can handle the given URL."""
        for platform, patterns in self.platform_patterns.items():
            for pattern in patterns:
                if re.match(pattern, url):
                    return True
        return False
    
    def _detect_platform(self, url: str) -> Platform:
        """Detect the platform from URL."""
        for platform, patterns in self.platform_patterns.items():
            for pattern in patterns:
                if re.match(pattern, url):
                    return platform
        return Platform.UNKNOWN
    
    def _get_yt_dlp_options(self, extract_audio: bool = False) -> Dict[str, Any]:
        """Get yt-dlp options for download."""
        options = {
            'outtmpl': str(self.output_dir / '%(title)s.%(ext)s'),
            'writeinfojson': True,
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['zh', 'zh-CN', 'en'],
            'ignoreerrors': False,
            'no_warnings': False,
        }
        
        if self.cookies_file and Path(self.cookies_file).exists():
            options['cookiefile'] = self.cookies_file
        
        if extract_audio:
            options.update({
                'format': 'best[ext=mp4]/best',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'wav',
                    'preferredquality': '192',
                }],
                'keepvideo': True,
            })
        else:
            options['format'] = 'best[ext=mp4]/best'
        
        return options
    
    def get_video_info(self, url: str) -> VideoInfo:
        """Extract video information without downloading."""
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return VideoInfo(
                    url=url,
                    title=info.get('title', 'Unknown Title'),
                    duration=info.get('duration'),
                    uploader=info.get('uploader') or info.get('channel'),
                    upload_date=info.get('upload_date'),
                    description=info.get('description'),
                    thumbnail=info.get('thumbnail'),
                    view_count=info.get('view_count'),
                    like_count=info.get('like_count'),
                    platform=self._detect_platform(url),
                    video_id=info.get('id'),
                )
        except Exception as e:
            logger.error(f"Failed to extract video info: {e}")
            raise Exception(f"Failed to extract video info: {e}")
    
    def download(self, url: str, extract_audio: bool = True) -> DownloadResult:
        """Download video and optionally extract audio."""
        try:
            # Get video info first
            video_info = self.get_video_info(url)
            logger.info(f"Downloading: {video_info.title}")
            
            # Set up yt-dlp options
            options = self._get_yt_dlp_options(extract_audio)
            
            # Download with progress hook
            downloaded_files = []
            
            def progress_hook(d):
                if d['status'] == 'finished':
                    downloaded_files.append(d['filename'])
                    logger.info(f"Downloaded: {Path(d['filename']).name}")
                elif d['status'] == 'downloading':
                    if 'total_bytes' in d and d['total_bytes']:
                        percent = d['downloaded_bytes'] / d['total_bytes'] * 100
                        logger.info(f"Progress: {percent:.1f}%")
            
            options['progress_hooks'] = [progress_hook]
            
            with yt_dlp.YoutubeDL(options) as ydl:
                ydl.download([url])
            
            # Find downloaded files
            video_path = None
            audio_path = None
            
            for file_path in downloaded_files:
                path = Path(file_path)
                if path.suffix.lower() in ['.mp4', '.mkv', '.webm', '.avi']:
                    video_path = path
                elif path.suffix.lower() in ['.wav', '.mp3', '.m4a']:
                    audio_path = path
            
            # If no audio file found but we wanted audio, try to find it
            if extract_audio and not audio_path and video_path:
                # Look for audio file with same name but different extension
                audio_candidates = [
                    video_path.with_suffix('.wav'),
                    video_path.with_suffix('.mp3'),
                    video_path.with_suffix('.m4a'),
                ]
                for candidate in audio_candidates:
                    if candidate.exists():
                        audio_path = candidate
                        break
            
            if not video_path:
                # Try to find any video file with similar name
                safe_title = self._sanitize_filename(video_info.title)
                for ext in ['.mp4', '.mkv', '.webm', '.avi']:
                    candidate = self.output_dir / f"{safe_title}{ext}"
                    if candidate.exists():
                        video_path = candidate
                        break
            
            return DownloadResult(
                success=True,
                video_path=video_path,
                audio_path=audio_path,
                video_info=video_info,
            )
            
        except DownloadError as e:
            error_msg = f"Download failed: {e}"
            logger.error(error_msg)
            return DownloadResult(
                success=False,
                error_message=error_msg,
            )
        except Exception as e:
            error_msg = f"Unexpected error during download: {e}"
            logger.error(error_msg)
            return DownloadResult(
                success=False,
                error_message=error_msg,
            )
    
    def download_playlist(self, url: str, extract_audio: bool = True) -> List[DownloadResult]:
        """Download all videos from a playlist.
        
        Args:
            url: Playlist URL
            extract_audio: Whether to extract audio for each video
            
        Returns:
            List of download results
        """
        results = []
        
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                playlist_info = ydl.extract_info(url, download=False)
                
                if 'entries' in playlist_info:
                    for entry in playlist_info['entries']:
                        if entry:
                            video_url = entry.get('webpage_url') or entry.get('url')
                            if video_url:
                                result = self.download(video_url, extract_audio)
                                results.append(result)
                else:
                    # Single video, not a playlist
                    result = self.download(url, extract_audio)
                    results.append(result)
                    
        except Exception as e:
            logger.error(f"Failed to download playlist: {e}")
            results.append(DownloadResult(
                success=False,
                error_message=f"Playlist download failed: {e}",
            ))
        
        return results
