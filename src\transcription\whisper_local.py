"""Local Whisper transcription implementation."""

import time
from pathlib import Path
from typing import Optional, List
from loguru import logger

import torch
import whisper
from whisper.utils import get_writer

from .base import BaseTranscriber, TranscriptionResult, TranscriptionSegment


class WhisperLocalTranscriber(BaseTranscriber):
    """Local Whisper model transcriber."""
    
    def __init__(
        self,
        model_size: str = "base",
        device: Optional[str] = None,
        language: Optional[str] = None
    ):
        """Initialize local Whisper transcriber.
        
        Args:
            model_size: Whisper model size (tiny, base, small, medium, large, large-v2, large-v3)
            device: Device to use (auto, cpu, cuda)
            language: Target language for transcription
        """
        super().__init__(language)
        self.model_size = model_size
        self.device = self._determine_device(device)
        self.model = None
        
        logger.info(f"Initializing Whisper {model_size} on {self.device}")
    
    def _determine_device(self, device: Optional[str]) -> str:
        """Determine the best device to use for inference.
        
        Args:
            device: Requested device (auto, cpu, cuda)
            
        Returns:
            Device string to use
        """
        if device == "cpu":
            return "cpu"
        elif device == "cuda":
            if torch.cuda.is_available():
                return "cuda"
            else:
                logger.warning("CUDA requested but not available, falling back to CPU")
                return "cpu"
        else:  # auto
            if torch.cuda.is_available():
                logger.info("CUDA available, using GPU acceleration")
                return "cuda"
            else:
                logger.info("CUDA not available, using CPU")
                return "cpu"
    
    def _load_model(self):
        """Load the Whisper model if not already loaded."""
        if self.model is None:
            try:
                logger.info(f"Loading Whisper {self.model_size} model...")
                self.model = whisper.load_model(self.model_size, device=self.device)
                logger.info("Whisper model loaded successfully")
            except Exception as e:
                error_msg = f"Failed to load Whisper model: {e}"
                logger.error(error_msg)
                raise Exception(error_msg)
    
    def transcribe(self, audio_path: Path) -> TranscriptionResult:
        """Transcribe audio file using local Whisper model.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Transcription result
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        self._load_model()
        
        try:
            logger.info(f"Transcribing {audio_path.name} with Whisper {self.model_size}")
            start_time = time.time()
            
            # Transcribe with Whisper
            result = self.model.transcribe(
                str(audio_path),
                language=self.language,
                verbose=False,
                word_timestamps=True
            )
            
            processing_time = time.time() - start_time
            logger.info(f"Transcription completed in {processing_time:.2f}s")
            
            # Convert to our format
            segments = []
            for segment in result.get("segments", []):
                segments.append(TranscriptionSegment(
                    start=segment["start"],
                    end=segment["end"],
                    text=segment["text"],
                    confidence=segment.get("avg_logprob")
                ))
            
            return TranscriptionResult(
                text=result["text"],
                segments=segments,
                language=result.get("language"),
                processing_time=processing_time
            )
            
        except Exception as e:
            error_msg = f"Transcription failed: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def transcribe_with_timestamps(self, audio_path: Path) -> TranscriptionResult:
        """Transcribe with detailed word-level timestamps.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Transcription result with word-level timestamps
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        self._load_model()
        
        try:
            logger.info(f"Transcribing {audio_path.name} with word timestamps")
            start_time = time.time()
            
            result = self.model.transcribe(
                str(audio_path),
                language=self.language,
                verbose=False,
                word_timestamps=True
            )
            
            processing_time = time.time() - start_time
            
            # Convert segments with word-level detail
            segments = []
            for segment in result.get("segments", []):
                # Create segment with word details if available
                segment_text = segment["text"]
                if "words" in segment:
                    # Build text from words for better accuracy
                    words = [word["word"] for word in segment["words"]]
                    segment_text = "".join(words)
                
                segments.append(TranscriptionSegment(
                    start=segment["start"],
                    end=segment["end"],
                    text=segment_text,
                    confidence=segment.get("avg_logprob")
                ))
            
            return TranscriptionResult(
                text=result["text"],
                segments=segments,
                language=result.get("language"),
                processing_time=processing_time
            )
            
        except Exception as e:
            error_msg = f"Transcription with timestamps failed: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def is_available(self) -> bool:
        """Check if Whisper is available."""
        try:
            # Try to import whisper
            import whisper
            return True
        except ImportError:
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available Whisper models.
        
        Returns:
            List of model names
        """
        return ["tiny", "base", "small", "medium", "large", "large-v2", "large-v3"]
    
    def get_model_info(self) -> dict:
        """Get information about the current model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_size": self.model_size,
            "device": self.device,
            "language": self.language,
            "loaded": self.model is not None,
            "cuda_available": torch.cuda.is_available(),
        }
