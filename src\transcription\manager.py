"""Transcription manager for coordinating different transcription services."""

from pathlib import Path
from typing import Optional, List
from loguru import logger

from ..core.config import get_config
from .base import BaseTranscriber, TranscriptionResult, TranscriptionFormat
from .whisper_local import WhisperLocalTranscriber
from .whisper_api import WhisperAPITranscriber


class TranscriptionManager:
    """Manager for coordinating transcription services."""
    
    def __init__(self):
        """Initialize transcription manager."""
        self.config = get_config()
        self._transcriber: Optional[BaseTranscriber] = None
    
    def get_transcriber(self, use_local: Optional[bool] = None) -> BaseTranscriber:
        """Get the appropriate transcriber based on configuration.
        
        Args:
            use_local: Force local (True) or API (False) transcriber, or use config (None)
            
        Returns:
            Configured transcriber instance
        """
        if use_local is None:
            use_local = self.config.whisper.use_local
        
        if use_local:
            return self._get_local_transcriber()
        else:
            return self._get_api_transcriber()
    
    def _get_local_transcriber(self) -> WhisperLocalTranscriber:
        """Get local Whisper transcriber."""
        if not isinstance(self._transcriber, WhisperLocalTranscriber):
            logger.info("Initializing local Whisper transcriber")
            self._transcriber = WhisperLocalTranscriber(
                model_size=self.config.whisper.model_size,
                device=self.config.whisper.device,
                language=self.config.whisper.language
            )
        
        return self._transcriber
    
    def _get_api_transcriber(self) -> WhisperAPITranscriber:
        """Get OpenAI API transcriber."""
        if not isinstance(self._transcriber, WhisperAPITranscriber):
            logger.info("Initializing OpenAI Whisper API transcriber")
            self._transcriber = WhisperAPITranscriber(
                api_key=self.config.openai.api_key,
                language=self.config.whisper.language
            )
        
        return self._transcriber
    
    def transcribe(
        self,
        audio_path: Path,
        use_local: Optional[bool] = None,
        output_dir: Optional[Path] = None,
        formats: Optional[List[TranscriptionFormat]] = None
    ) -> TranscriptionResult:
        """Transcribe audio file and save results.
        
        Args:
            audio_path: Path to audio file
            use_local: Force local (True) or API (False) transcriber
            output_dir: Directory to save transcription files
            formats: Output formats to save
            
        Returns:
            Transcription result
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        # Get transcriber
        transcriber = self.get_transcriber(use_local)
        
        # Check if transcriber is available
        if not transcriber.is_available():
            transcriber_type = "local" if use_local else "API"
            raise Exception(f"Whisper {transcriber_type} transcriber is not available")
        
        # Transcribe
        logger.info(f"Starting transcription of {audio_path.name}")
        result = transcriber.transcribe(audio_path)
        
        # Save results if output directory specified
        if output_dir:
            self.save_transcription_results(result, audio_path, output_dir, formats)
        
        return result
    
    def save_transcription_results(
        self,
        result: TranscriptionResult,
        audio_path: Path,
        output_dir: Path,
        formats: Optional[List[TranscriptionFormat]] = None
    ) -> List[Path]:
        """Save transcription results in multiple formats.
        
        Args:
            result: Transcription result to save
            audio_path: Original audio file path (for naming)
            output_dir: Directory to save files
            formats: Output formats to save
            
        Returns:
            List of saved file paths
        """
        if formats is None:
            # Use formats from config
            format_names = self.config.transcription.formats
            formats = [TranscriptionFormat(fmt) for fmt in format_names]
        
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        saved_files = []
        base_name = audio_path.stem
        
        for format in formats:
            output_path = output_dir / f"{base_name}.{format.value}"
            
            try:
                # Use the transcriber's save method
                transcriber = self.get_transcriber()
                saved_path = transcriber.save_transcription(result, output_path, format)
                saved_files.append(saved_path)
                logger.info(f"Saved transcription: {saved_path.name}")
            except Exception as e:
                logger.error(f"Failed to save {format.value} format: {e}")
        
        return saved_files
    
    def transcribe_batch(
        self,
        audio_paths: List[Path],
        use_local: Optional[bool] = None,
        output_dir: Optional[Path] = None
    ) -> List[TranscriptionResult]:
        """Transcribe multiple audio files.
        
        Args:
            audio_paths: List of audio file paths
            use_local: Force local (True) or API (False) transcriber
            output_dir: Directory to save transcription files
            
        Returns:
            List of transcription results
        """
        results = []
        
        for audio_path in audio_paths:
            try:
                result = self.transcribe(audio_path, use_local, output_dir)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to transcribe {audio_path.name}: {e}")
                # Create empty result for failed transcription
                results.append(TranscriptionResult(
                    text=f"Transcription failed: {e}",
                    segments=[],
                    language=None,
                    processing_time=0.0
                ))
        
        return results
    
    def get_transcriber_info(self, use_local: Optional[bool] = None) -> dict:
        """Get information about the current transcriber.
        
        Args:
            use_local: Force local (True) or API (False) transcriber
            
        Returns:
            Dictionary with transcriber information
        """
        transcriber = self.get_transcriber(use_local)
        
        info = {
            "type": "local" if isinstance(transcriber, WhisperLocalTranscriber) else "api",
            "available": transcriber.is_available(),
            "language": transcriber.language,
        }
        
        if isinstance(transcriber, WhisperLocalTranscriber):
            info.update(transcriber.get_model_info())
        elif isinstance(transcriber, WhisperAPITranscriber):
            info.update({
                "supported_formats": transcriber.get_supported_formats(),
                "model": transcriber.model,
            })
        
        return info
