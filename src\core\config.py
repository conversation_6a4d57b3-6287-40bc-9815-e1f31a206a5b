"""Configuration management for the video processing system."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv


class OpenAIConfig(BaseModel):
    """OpenAI API configuration."""
    api_key: str
    model: str = "gpt-4"
    temperature: float = 0.7
    max_tokens: int = 1000


class WhisperConfig(BaseModel):
    """Whisper transcription configuration."""
    use_local: bool = True
    model_size: str = "base"
    language: Optional[str] = None
    device: str = "auto"


class DownloadConfig(BaseModel):
    """Video download configuration."""
    output_dir: str = "./downloads"
    keep_video: bool = True
    quality: str = "best"


class TranscriptionConfig(BaseModel):
    """Transcription output configuration."""
    output_dir: str = "./transcriptions"
    formats: list[str] = ["txt", "srt"]
    include_timestamps: bool = True


class SummarizationConfig(BaseModel):
    """Summarization configuration."""
    output_dir: str = "./summaries"
    default_prompt: str = Field(default="Please provide a comprehensive summary of the following video transcript.")
    prompts: Dict[str, str] = Field(default_factory=dict)


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    file: str = "./logs/app.log"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class PlatformConfig(BaseModel):
    """Platform-specific configuration."""
    cookies: Optional[str] = None
    user_agent: Optional[str] = None


class PlatformsConfig(BaseModel):
    """All platform configurations."""
    bilibili: PlatformConfig = Field(default_factory=PlatformConfig)
    douyin: PlatformConfig = Field(default_factory=PlatformConfig)


class Config(BaseModel):
    """Main configuration class."""
    openai: OpenAIConfig
    whisper: WhisperConfig = Field(default_factory=WhisperConfig)
    download: DownloadConfig = Field(default_factory=DownloadConfig)
    transcription: TranscriptionConfig = Field(default_factory=TranscriptionConfig)
    summarization: SummarizationConfig = Field(default_factory=SummarizationConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    platforms: PlatformsConfig = Field(default_factory=PlatformsConfig)


class ConfigManager:
    """Configuration manager for loading and managing application settings."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file. Defaults to config/config.yaml
        """
        self.config_path = config_path or "config/config.yaml"
        self._config: Optional[Config] = None
        
        # Load environment variables
        load_dotenv()
    
    def load_config(self) -> Config:
        """Load configuration from file and environment variables.
        
        Returns:
            Loaded configuration object
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If configuration is invalid
        """
        if self._config is not None:
            return self._config
        
        # Load from YAML file
        config_data = self._load_yaml_config()
        
        # Override with environment variables
        config_data = self._apply_env_overrides(config_data)
        
        # Validate and create config object
        self._config = Config(**config_data)
        
        # Create output directories
        self._create_directories()
        
        return self._config
    
    def _load_yaml_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        config_path = Path(self.config_path)
        
        if not config_path.exists():
            # Try to copy from example if config doesn't exist
            example_path = Path("config/config.example.yaml")
            if example_path.exists():
                import shutil
                config_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy(example_path, config_path)
                print(f"Created config file from example: {config_path}")
            else:
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _apply_env_overrides(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        # OpenAI API key
        if api_key := os.getenv("OPENAI_API_KEY"):
            config_data.setdefault("openai", {})["api_key"] = api_key
        
        # Logging level
        if log_level := os.getenv("LOG_LEVEL"):
            config_data.setdefault("logging", {})["level"] = log_level
        
        # Output directories
        if download_dir := os.getenv("DOWNLOAD_DIR"):
            config_data.setdefault("download", {})["output_dir"] = download_dir
        
        if transcription_dir := os.getenv("TRANSCRIPTION_DIR"):
            config_data.setdefault("transcription", {})["output_dir"] = transcription_dir
        
        if summary_dir := os.getenv("SUMMARY_DIR"):
            config_data.setdefault("summarization", {})["output_dir"] = summary_dir
        
        if log_dir := os.getenv("LOG_DIR"):
            log_file = os.path.join(log_dir, "app.log")
            config_data.setdefault("logging", {})["file"] = log_file
        
        return config_data
    
    def _create_directories(self):
        """Create necessary output directories."""
        if self._config is None:
            return
        
        directories = [
            self._config.download.output_dir,
            self._config.transcription.output_dir,
            self._config.summarization.output_dir,
            Path(self._config.logging.file).parent,
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @property
    def config(self) -> Config:
        """Get the current configuration, loading it if necessary."""
        if self._config is None:
            return self.load_config()
        return self._config


# Global configuration manager instance
config_manager = ConfigManager()


def get_config() -> Config:
    """Get the global configuration instance."""
    return config_manager.config
