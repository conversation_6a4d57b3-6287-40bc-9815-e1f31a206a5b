"""Base classes for transcription services."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional, Dict, Any
from enum import Enum


class TranscriptionFormat(Enum):
    """Supported transcription output formats."""
    TEXT = "txt"
    SRT = "srt"
    VTT = "vtt"
    JSON = "json"


@dataclass
class TranscriptionSegment:
    """A segment of transcribed text with timing information."""
    start: float
    end: float
    text: str
    confidence: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "start": self.start,
            "end": self.end,
            "text": self.text,
            "confidence": self.confidence,
        }


@dataclass
class TranscriptionResult:
    """Result of transcription operation."""
    text: str
    segments: List[TranscriptionSegment]
    language: Optional[str] = None
    confidence: Optional[float] = None
    processing_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "text": self.text,
            "segments": [seg.to_dict() for seg in self.segments],
            "language": self.language,
            "confidence": self.confidence,
            "processing_time": self.processing_time,
        }
    
    def to_srt(self) -> str:
        """Convert to SRT subtitle format."""
        srt_content = []
        
        for i, segment in enumerate(self.segments, 1):
            start_time = self._seconds_to_srt_time(segment.start)
            end_time = self._seconds_to_srt_time(segment.end)
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(segment.text.strip())
            srt_content.append("")  # Empty line between segments
        
        return "\n".join(srt_content)
    
    def to_vtt(self) -> str:
        """Convert to WebVTT subtitle format."""
        vtt_content = ["WEBVTT", ""]
        
        for segment in self.segments:
            start_time = self._seconds_to_vtt_time(segment.start)
            end_time = self._seconds_to_vtt_time(segment.end)
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(segment.text.strip())
            vtt_content.append("")  # Empty line between segments
        
        return "\n".join(vtt_content)
    
    @staticmethod
    def _seconds_to_srt_time(seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    @staticmethod
    def _seconds_to_vtt_time(seconds: float) -> str:
        """Convert seconds to WebVTT time format (HH:MM:SS.mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"


class BaseTranscriber(ABC):
    """Abstract base class for transcription services."""
    
    def __init__(self, language: Optional[str] = None):
        """Initialize transcriber.
        
        Args:
            language: Target language for transcription (auto-detect if None)
        """
        self.language = language
    
    @abstractmethod
    def transcribe(self, audio_path: Path) -> TranscriptionResult:
        """Transcribe audio file to text.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Transcription result with text and segments
            
        Raises:
            Exception: If transcription fails
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the transcription service is available.
        
        Returns:
            True if service is available, False otherwise
        """
        pass
    
    def save_transcription(
        self,
        result: TranscriptionResult,
        output_path: Path,
        format: TranscriptionFormat = TranscriptionFormat.TEXT
    ) -> Path:
        """Save transcription result to file.
        
        Args:
            result: Transcription result to save
            output_path: Output file path (extension will be adjusted for format)
            format: Output format
            
        Returns:
            Path to saved file
        """
        # Adjust file extension based on format
        output_path = output_path.with_suffix(f".{format.value}")
        
        # Generate content based on format
        if format == TranscriptionFormat.TEXT:
            content = result.text
        elif format == TranscriptionFormat.SRT:
            content = result.to_srt()
        elif format == TranscriptionFormat.VTT:
            content = result.to_vtt()
        elif format == TranscriptionFormat.JSON:
            import json
            content = json.dumps(result.to_dict(), indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        # Save to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return output_path
