"""Base classes for video downloaders."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Optional, Dict, Any
from enum import Enum


class Platform(Enum):
    """Supported video platforms."""
    BILIBILI = "bilibili"
    DOUYIN = "douyin"
    UNKNOWN = "unknown"


@dataclass
class VideoInfo:
    """Video information container."""
    url: str
    title: str
    duration: Optional[float] = None
    uploader: Optional[str] = None
    upload_date: Optional[str] = None
    description: Optional[str] = None
    thumbnail: Optional[str] = None
    view_count: Optional[int] = None
    like_count: Optional[int] = None
    platform: Platform = Platform.UNKNOWN
    video_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "url": self.url,
            "title": self.title,
            "duration": self.duration,
            "uploader": self.uploader,
            "upload_date": self.upload_date,
            "description": self.description,
            "thumbnail": self.thumbnail,
            "view_count": self.view_count,
            "like_count": self.like_count,
            "platform": self.platform.value,
            "video_id": self.video_id,
        }


@dataclass
class DownloadResult:
    """Download operation result."""
    success: bool
    video_path: Optional[Path] = None
    audio_path: Optional[Path] = None
    video_info: Optional[VideoInfo] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "success": self.success,
            "video_path": str(self.video_path) if self.video_path else None,
            "audio_path": str(self.audio_path) if self.audio_path else None,
            "video_info": self.video_info.to_dict() if self.video_info else None,
            "error_message": self.error_message,
        }


class BaseDownloader(ABC):
    """Abstract base class for video downloaders."""
    
    def __init__(self, output_dir: str = "./downloads"):
        """Initialize downloader.
        
        Args:
            output_dir: Directory to save downloaded videos
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    @abstractmethod
    def can_handle(self, url: str) -> bool:
        """Check if this downloader can handle the given URL.
        
        Args:
            url: Video URL to check
            
        Returns:
            True if this downloader can handle the URL
        """
        pass
    
    @abstractmethod
    def get_video_info(self, url: str) -> VideoInfo:
        """Extract video information without downloading.
        
        Args:
            url: Video URL
            
        Returns:
            Video information
            
        Raises:
            Exception: If unable to extract video info
        """
        pass
    
    @abstractmethod
    def download(self, url: str, extract_audio: bool = True) -> DownloadResult:
        """Download video and optionally extract audio.
        
        Args:
            url: Video URL to download
            extract_audio: Whether to extract audio for transcription
            
        Returns:
            Download result with paths and metadata
        """
        pass
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        if len(filename) > 200:
            filename = filename[:200]
        
        return filename.strip()
    
    def _get_output_path(self, video_info: VideoInfo, extension: str) -> Path:
        """Generate output path for downloaded file.
        
        Args:
            video_info: Video information
            extension: File extension (e.g., 'mp4', 'mp3')
            
        Returns:
            Output file path
        """
        safe_title = self._sanitize_filename(video_info.title)
        filename = f"{safe_title}.{extension}"
        return self.output_dir / filename
