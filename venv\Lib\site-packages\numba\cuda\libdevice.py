def abs(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_abs.html

    :param x: Argument.
    :type x: int32
    :rtype: int32
"""


def acos(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_acos.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def acosf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_acosf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def acosh(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_acosh.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def acoshf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_acoshf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def asin(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_asin.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def asinf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_asinf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def asinh(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_asinh.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def asinhf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_asinhf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def atan(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_atan.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def atan2(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_atan2.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def atan2f(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_atan2f.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def atanf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_atanf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def atanh(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_atanh.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def atanhf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_atanhf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def brev(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_brev.html

    :param x: Argument.
    :type x: int32
    :rtype: int32
"""


def brevll(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_brevll.html

    :param x: Argument.
    :type x: int64
    :rtype: int64
"""


def byte_perm(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_byte_perm.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :param z: Argument.
    :type z: int32
    :rtype: int32
"""


def cbrt(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cbrt.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def cbrtf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cbrtf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def ceil(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ceil.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def ceilf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ceilf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def clz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_clz.html

    :param x: Argument.
    :type x: int32
    :rtype: int32
"""


def clzll(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_clzll.html

    :param x: Argument.
    :type x: int64
    :rtype: int32
"""


def copysign(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_copysign.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def copysignf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_copysignf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def cos(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cos.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def cosf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cosf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def cosh(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cosh.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def coshf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_coshf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def cospi(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cospi.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def cospif(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_cospif.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def dadd_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dadd_rd.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dadd_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dadd_rn.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dadd_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dadd_ru.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dadd_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dadd_rz.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def ddiv_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ddiv_rd.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def ddiv_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ddiv_rn.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def ddiv_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ddiv_ru.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def ddiv_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ddiv_rz.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dmul_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dmul_rd.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dmul_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dmul_rn.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dmul_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dmul_ru.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def dmul_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dmul_rz.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def double2float_rd(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2float_rd.html

    :param d: Argument.
    :type d: float64
    :rtype: float32
"""


def double2float_rn(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2float_rn.html

    :param d: Argument.
    :type d: float64
    :rtype: float32
"""


def double2float_ru(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2float_ru.html

    :param d: Argument.
    :type d: float64
    :rtype: float32
"""


def double2float_rz(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2float_rz.html

    :param d: Argument.
    :type d: float64
    :rtype: float32
"""


def double2hiint(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2hiint.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2int_rd(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2int_rd.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2int_rn(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2int_rn.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2int_ru(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2int_ru.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2int_rz(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2int_rz.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2ll_rd(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ll_rd.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2ll_rn(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ll_rn.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2ll_ru(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ll_ru.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2ll_rz(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ll_rz.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2loint(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2loint.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2uint_rd(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2uint_rd.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2uint_rn(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2uint_rn.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2uint_ru(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2uint_ru.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2uint_rz(d):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2uint_rz.html

    :param d: Argument.
    :type d: float64
    :rtype: int32
"""


def double2ull_rd(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ull_rd.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2ull_rn(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ull_rn.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2ull_ru(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ull_ru.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double2ull_rz(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double2ull_rz.html

    :param f: Argument.
    :type f: float64
    :rtype: int64
"""


def double_as_longlong(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_double_as_longlong.html

    :param x: Argument.
    :type x: float64
    :rtype: int64
"""


def drcp_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_drcp_rd.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def drcp_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_drcp_rn.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def drcp_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_drcp_ru.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def drcp_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_drcp_rz.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def dsqrt_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dsqrt_rd.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def dsqrt_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dsqrt_rn.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def dsqrt_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dsqrt_ru.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def dsqrt_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_dsqrt_rz.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def erf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erf.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def erfc(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfc.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def erfcf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfcf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def erfcinv(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfcinv.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def erfcinvf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfcinvf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def erfcx(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfcx.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def erfcxf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfcxf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def erff(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erff.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def erfinv(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfinv.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def erfinvf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_erfinvf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def exp(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_exp.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def exp10(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_exp10.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def exp10f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_exp10f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def exp2(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_exp2.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def exp2f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_exp2f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def expf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_expf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def expm1(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_expm1.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def expm1f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_expm1f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fabs(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fabs.html

    :param f: Argument.
    :type f: float64
    :rtype: float64
"""


def fabsf(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fabsf.html

    :param f: Argument.
    :type f: float32
    :rtype: float32
"""


def fadd_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fadd_rd.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fadd_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fadd_rn.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fadd_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fadd_ru.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fadd_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fadd_rz.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fast_cosf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_cosf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_exp10f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_exp10f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_expf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_expf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_fdividef(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_fdividef.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fast_log10f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_log10f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_log2f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_log2f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_logf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_logf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_powf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_powf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fast_sincosf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_sincosf.html

    :param x: Argument.
    :type x: float32
    :rtype: UniTuple(float32 x 2)
"""


def fast_sinf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_sinf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fast_tanf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fast_tanf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fdim(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fdim.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def fdimf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fdimf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fdiv_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fdiv_rd.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fdiv_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fdiv_rn.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fdiv_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fdiv_ru.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fdiv_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fdiv_rz.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def ffs(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ffs.html

    :param x: Argument.
    :type x: int32
    :rtype: int32
"""


def ffsll(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ffsll.html

    :param x: Argument.
    :type x: int64
    :rtype: int32
"""


def finitef(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_finitef.html

    :param x: Argument.
    :type x: float32
    :rtype: int32
"""


def float2half_rn(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2half_rn.html

    :param f: Argument.
    :type f: float32
    :rtype: int16
"""


def float2int_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2int_rd.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2int_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2int_rn.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2int_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2int_ru.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2int_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2int_rz.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2ll_rd(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ll_rd.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2ll_rn(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ll_rn.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2ll_ru(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ll_ru.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2ll_rz(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ll_rz.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2uint_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2uint_rd.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2uint_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2uint_rn.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2uint_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2uint_ru.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2uint_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2uint_rz.html

    :param in: Argument.
    :type in: float32
    :rtype: int32
"""


def float2ull_rd(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ull_rd.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2ull_rn(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ull_rn.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2ull_ru(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ull_ru.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float2ull_rz(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float2ull_rz.html

    :param f: Argument.
    :type f: float32
    :rtype: int64
"""


def float_as_int(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_float_as_int.html

    :param x: Argument.
    :type x: float32
    :rtype: int32
"""


def floor(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_floor.html

    :param f: Argument.
    :type f: float64
    :rtype: float64
"""


def floorf(f):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_floorf.html

    :param f: Argument.
    :type f: float32
    :rtype: float32
"""


def fma(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fma.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :param z: Argument.
    :type z: float64
    :rtype: float64
"""


def fma_rd(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fma_rd.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :param z: Argument.
    :type z: float64
    :rtype: float64
"""


def fma_rn(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fma_rn.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :param z: Argument.
    :type z: float64
    :rtype: float64
"""


def fma_ru(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fma_ru.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :param z: Argument.
    :type z: float64
    :rtype: float64
"""


def fma_rz(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fma_rz.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :param z: Argument.
    :type z: float64
    :rtype: float64
"""


def fmaf(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmaf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :param z: Argument.
    :type z: float32
    :rtype: float32
"""


def fmaf_rd(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmaf_rd.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :param z: Argument.
    :type z: float32
    :rtype: float32
"""


def fmaf_rn(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmaf_rn.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :param z: Argument.
    :type z: float32
    :rtype: float32
"""


def fmaf_ru(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmaf_ru.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :param z: Argument.
    :type z: float32
    :rtype: float32
"""


def fmaf_rz(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmaf_rz.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :param z: Argument.
    :type z: float32
    :rtype: float32
"""


def fmax(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmax.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def fmaxf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmaxf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fmin(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmin.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def fminf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fminf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fmod(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmod.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def fmodf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmodf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fmul_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmul_rd.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fmul_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmul_rn.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fmul_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmul_ru.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fmul_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fmul_rz.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def frcp_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frcp_rd.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def frcp_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frcp_rn.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def frcp_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frcp_ru.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def frcp_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frcp_rz.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def frexp(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frexp.html

    :param x: Argument.
    :type x: float64
    :rtype: Tuple(float64, int32)
"""


def frexpf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frexpf.html

    :param x: Argument.
    :type x: float32
    :rtype: Tuple(float32, int32)
"""


def frsqrt_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_frsqrt_rn.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fsqrt_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsqrt_rd.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fsqrt_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsqrt_rn.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fsqrt_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsqrt_ru.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fsqrt_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsqrt_rz.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def fsub_rd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsub_rd.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fsub_rn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsub_rn.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fsub_ru(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsub_ru.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def fsub_rz(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_fsub_rz.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def hadd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_hadd.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def half2float(h):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_half2float.html

    :param h: Argument.
    :type h: int16
    :rtype: float32
"""


def hiloint2double(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_hiloint2double.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: float64
"""


def hypot(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_hypot.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def hypotf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_hypotf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def ilogb(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ilogb.html

    :param x: Argument.
    :type x: float64
    :rtype: int32
"""


def ilogbf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ilogbf.html

    :param x: Argument.
    :type x: float32
    :rtype: int32
"""


def int2double_rn(i):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_int2double_rn.html

    :param i: Argument.
    :type i: int32
    :rtype: float64
"""


def int2float_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_int2float_rd.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def int2float_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_int2float_rn.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def int2float_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_int2float_ru.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def int2float_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_int2float_rz.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def int_as_float(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_int_as_float.html

    :param x: Argument.
    :type x: int32
    :rtype: float32
"""


def isfinited(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_isfinited.html

    :param x: Argument.
    :type x: float64
    :rtype: int32
"""


def isinfd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_isinfd.html

    :param x: Argument.
    :type x: float64
    :rtype: int32
"""


def isinff(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_isinff.html

    :param x: Argument.
    :type x: float32
    :rtype: int32
"""


def isnand(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_isnand.html

    :param x: Argument.
    :type x: float64
    :rtype: int32
"""


def isnanf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_isnanf.html

    :param x: Argument.
    :type x: float32
    :rtype: int32
"""


def j0(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_j0.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def j0f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_j0f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def j1(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_j1.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def j1f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_j1f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def jn(n, x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_jn.html

    :param n: Argument.
    :type n: int32
    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def jnf(n, x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_jnf.html

    :param n: Argument.
    :type n: int32
    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def ldexp(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ldexp.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: int32
    :rtype: float64
"""


def ldexpf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ldexpf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: int32
    :rtype: float32
"""


def lgamma(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_lgamma.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def lgammaf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_lgammaf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def ll2double_rd(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2double_rd.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ll2double_rn(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2double_rn.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ll2double_ru(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2double_ru.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ll2double_rz(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2double_rz.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ll2float_rd(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2float_rd.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ll2float_rn(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2float_rn.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ll2float_ru(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2float_ru.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ll2float_rz(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ll2float_rz.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def llabs(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llabs.html

    :param x: Argument.
    :type x: int64
    :rtype: int64
"""


def llmax(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llmax.html

    :param x: Argument.
    :type x: int64
    :param y: Argument.
    :type y: int64
    :rtype: int64
"""


def llmin(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llmin.html

    :param x: Argument.
    :type x: int64
    :param y: Argument.
    :type y: int64
    :rtype: int64
"""


def llrint(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llrint.html

    :param x: Argument.
    :type x: float64
    :rtype: int64
"""


def llrintf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llrintf.html

    :param x: Argument.
    :type x: float32
    :rtype: int64
"""


def llround(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llround.html

    :param x: Argument.
    :type x: float64
    :rtype: int64
"""


def llroundf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_llroundf.html

    :param x: Argument.
    :type x: float32
    :rtype: int64
"""


def log(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def log10(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log10.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def log10f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log10f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def log1p(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log1p.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def log1pf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log1pf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def log2(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log2.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def log2f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_log2f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def logb(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_logb.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def logbf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_logbf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def logf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_logf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def longlong_as_double(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_longlong_as_double.html

    :param x: Argument.
    :type x: int64
    :rtype: float64
"""


def max(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_max.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def min(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_min.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def modf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_modf.html

    :param x: Argument.
    :type x: float64
    :rtype: UniTuple(float64 x 2)
"""


def modff(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_modff.html

    :param x: Argument.
    :type x: float32
    :rtype: UniTuple(float32 x 2)
"""


def mul24(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_mul24.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def mul64hi(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_mul64hi.html

    :param x: Argument.
    :type x: int64
    :param y: Argument.
    :type y: int64
    :rtype: int64
"""


def mulhi(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_mulhi.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def nearbyint(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_nearbyint.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def nearbyintf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_nearbyintf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def nextafter(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_nextafter.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def nextafterf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_nextafterf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def normcdf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_normcdf.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def normcdff(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_normcdff.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def normcdfinv(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_normcdfinv.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def normcdfinvf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_normcdfinvf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def popc(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_popc.html

    :param x: Argument.
    :type x: int32
    :rtype: int32
"""


def popcll(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_popcll.html

    :param x: Argument.
    :type x: int64
    :rtype: int32
"""


def pow(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_pow.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def powf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_powf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def powi(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_powi.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: int32
    :rtype: float64
"""


def powif(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_powif.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: int32
    :rtype: float32
"""


def rcbrt(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rcbrt.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def rcbrtf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rcbrtf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def remainder(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_remainder.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: float64
"""


def remainderf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_remainderf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: float32
"""


def remquo(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_remquo.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: float64
    :rtype: Tuple(float64, int32)
"""


def remquof(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_remquof.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: float32
    :rtype: Tuple(float32, int32)
"""


def rhadd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rhadd.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def rint(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rint.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def rintf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rintf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def round(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_round.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def roundf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_roundf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def rsqrt(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rsqrt.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def rsqrtf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_rsqrtf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def sad(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sad.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :param z: Argument.
    :type z: int32
    :rtype: int32
"""


def saturatef(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_saturatef.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def scalbn(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_scalbn.html

    :param x: Argument.
    :type x: float64
    :param y: Argument.
    :type y: int32
    :rtype: float64
"""


def scalbnf(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_scalbnf.html

    :param x: Argument.
    :type x: float32
    :param y: Argument.
    :type y: int32
    :rtype: float32
"""


def signbitd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_signbitd.html

    :param x: Argument.
    :type x: float64
    :rtype: int32
"""


def signbitf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_signbitf.html

    :param x: Argument.
    :type x: float32
    :rtype: int32
"""


def sin(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sin.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def sincos(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sincos.html

    :param x: Argument.
    :type x: float64
    :rtype: UniTuple(float64 x 2)
"""


def sincosf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sincosf.html

    :param x: Argument.
    :type x: float32
    :rtype: UniTuple(float32 x 2)
"""


def sincospi(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sincospi.html

    :param x: Argument.
    :type x: float64
    :rtype: UniTuple(float64 x 2)
"""


def sincospif(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sincospif.html

    :param x: Argument.
    :type x: float32
    :rtype: UniTuple(float32 x 2)
"""


def sinf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sinf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def sinh(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sinh.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def sinhf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sinhf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def sinpi(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sinpi.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def sinpif(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sinpif.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def sqrt(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sqrt.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def sqrtf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_sqrtf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def tan(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_tan.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def tanf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_tanf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def tanh(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_tanh.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def tanhf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_tanhf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def tgamma(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_tgamma.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def tgammaf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_tgammaf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def trunc(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_trunc.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def truncf(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_truncf.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def uhadd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_uhadd.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def uint2double_rn(i):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_uint2double_rn.html

    :param i: Argument.
    :type i: int32
    :rtype: float64
"""


def uint2float_rd(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_uint2float_rd.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def uint2float_rn(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_uint2float_rn.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def uint2float_ru(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_uint2float_ru.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def uint2float_rz(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_uint2float_rz.html

    :param in: Argument.
    :type in: int32
    :rtype: float32
"""


def ull2double_rd(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2double_rd.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ull2double_rn(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2double_rn.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ull2double_ru(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2double_ru.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ull2double_rz(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2double_rz.html

    :param l: Argument.
    :type l: int64
    :rtype: float64
"""


def ull2float_rd(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2float_rd.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ull2float_rn(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2float_rn.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ull2float_ru(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2float_ru.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ull2float_rz(l):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ull2float_rz.html

    :param l: Argument.
    :type l: int64
    :rtype: float32
"""


def ullmax(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ullmax.html

    :param x: Argument.
    :type x: int64
    :param y: Argument.
    :type y: int64
    :rtype: int64
"""


def ullmin(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ullmin.html

    :param x: Argument.
    :type x: int64
    :param y: Argument.
    :type y: int64
    :rtype: int64
"""


def umax(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_umax.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def umin(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_umin.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def umul24(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_umul24.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def umul64hi(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_umul64hi.html

    :param x: Argument.
    :type x: int64
    :param y: Argument.
    :type y: int64
    :rtype: int64
"""


def umulhi(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_umulhi.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def urhadd(x, y):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_urhadd.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :rtype: int32
"""


def usad(x, y, z):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_usad.html

    :param x: Argument.
    :type x: int32
    :param y: Argument.
    :type y: int32
    :param z: Argument.
    :type z: int32
    :rtype: int32
"""


def y0(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_y0.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def y0f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_y0f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def y1(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_y1.html

    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def y1f(x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_y1f.html

    :param x: Argument.
    :type x: float32
    :rtype: float32
"""


def yn(n, x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_yn.html

    :param n: Argument.
    :type n: int32
    :param x: Argument.
    :type x: float64
    :rtype: float64
"""


def ynf(n, x):
    """
    See https://docs.nvidia.com/cuda/libdevice-users-guide/__nv_ynf.html

    :param n: Argument.
    :type n: int32
    :param x: Argument.
    :type x: float32
    :rtype: float32
"""
