"""GPT-based text summarization implementation."""

import time
from typing import Optional, Dict, Any
from loguru import logger

import openai
from openai import OpenAI

from .base import BaseSummarizer, SummaryResult, SummaryType


class GPTSummarizer(BaseSummarizer):
    """GPT-based text summarizer using OpenAI API."""
    
    def __init__(
        self,
        api_key: str,
        model: str = "gpt-4",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        base_url: Optional[str] = None
    ):
        """Initialize GPT summarizer.
        
        Args:
            api_key: OpenAI API key
            model: GPT model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            base_url: Custom API base URL
        """
        super().__init__(model)
        self.api_key = api_key
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.base_url = base_url
        
        # Initialize OpenAI client
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        logger.info(f"Initialized GPT summarizer with model {model}")
    
    def summarize(
        self,
        text: str,
        summary_type: SummaryType = SummaryType.BRIEF,
        custom_prompt: Optional[str] = None,
        **kwargs
    ) -> SummaryResult:
        """Summarize text using GPT.
        
        Args:
            text: Text to summarize
            summary_type: Type of summary to generate
            custom_prompt: Custom prompt for summarization
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Returns:
            Summary result
        """
        if not text.strip():
            raise ValueError("Input text is empty")
        
        # Prepare text
        prepared_text = self.prepare_text(text)
        
        # Get prompt
        if custom_prompt:
            prompt = custom_prompt.format(text=prepared_text)
        else:
            prompt_template = self.get_prompt_template(summary_type)
            prompt = prompt_template.format(text=prepared_text)
        
        # Extract parameters
        temperature = kwargs.get('temperature', self.temperature)
        max_tokens = kwargs.get('max_tokens', self.max_tokens)
        
        try:
            logger.info(f"Generating {summary_type.value} summary with {self.model}")
            start_time = time.time()
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that creates clear, concise, and accurate summaries of text content."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            processing_time = time.time() - start_time
            summary_text = response.choices[0].message.content.strip()
            
            logger.info(f"Summary generated in {processing_time:.2f}s")
            
            return SummaryResult.from_text(
                original_text=text,
                summary_text=summary_text,
                summary_type=summary_type,
                processing_time=processing_time,
                model_used=self.model,
                prompt_used=prompt
            )
            
        except openai.APIError as e:
            error_msg = f"OpenAI API error: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Summarization failed: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def summarize_long_text(
        self,
        text: str,
        summary_type: SummaryType = SummaryType.BRIEF,
        custom_prompt: Optional[str] = None,
        chunk_size: int = 3000,
        **kwargs
    ) -> SummaryResult:
        """Summarize long text by splitting into chunks.
        
        Args:
            text: Long text to summarize
            summary_type: Type of summary to generate
            custom_prompt: Custom prompt for summarization
            chunk_size: Maximum tokens per chunk
            **kwargs: Additional parameters
            
        Returns:
            Summary result
        """
        if self.estimate_tokens(text) <= chunk_size:
            # Text is short enough, summarize directly
            return self.summarize(text, summary_type, custom_prompt, **kwargs)
        
        logger.info("Text is long, splitting into chunks for summarization")
        
        # Split text into chunks
        chunks = self.split_long_text(text, chunk_size)
        chunk_summaries = []
        
        # Summarize each chunk
        for i, chunk in enumerate(chunks):
            logger.info(f"Summarizing chunk {i+1}/{len(chunks)}")
            try:
                chunk_result = self.summarize(chunk, summary_type, custom_prompt, **kwargs)
                chunk_summaries.append(chunk_result.summary)
            except Exception as e:
                logger.error(f"Failed to summarize chunk {i+1}: {e}")
                chunk_summaries.append(f"[Chunk {i+1} summarization failed]")
        
        # Combine chunk summaries
        combined_summary = "\n\n".join(chunk_summaries)
        
        # If combined summary is still long, summarize it again
        if self.estimate_tokens(combined_summary) > chunk_size:
            logger.info("Combined summary is long, creating final summary")
            final_prompt = f"""The following are summaries of different parts of a longer text. 
Please create a cohesive {summary_type.value} summary that combines the key points:

{combined_summary}

Final Summary:"""
            
            final_result = self.summarize(
                combined_summary,
                summary_type,
                final_prompt,
                **kwargs
            )
            final_summary = final_result.summary
        else:
            final_summary = combined_summary
        
        return SummaryResult.from_text(
            original_text=text,
            summary_text=final_summary,
            summary_type=summary_type,
            model_used=self.model,
            prompt_used=custom_prompt or self.get_prompt_template(summary_type)
        )
    
    def generate_multiple_summaries(
        self,
        text: str,
        summary_types: list[SummaryType],
        **kwargs
    ) -> Dict[SummaryType, SummaryResult]:
        """Generate multiple types of summaries for the same text.
        
        Args:
            text: Text to summarize
            summary_types: List of summary types to generate
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping summary types to results
        """
        results = {}
        
        for summary_type in summary_types:
            try:
                logger.info(f"Generating {summary_type.value} summary")
                result = self.summarize(text, summary_type, **kwargs)
                results[summary_type] = result
            except Exception as e:
                logger.error(f"Failed to generate {summary_type.value} summary: {e}")
                # Create error result
                results[summary_type] = SummaryResult.from_text(
                    original_text=text,
                    summary_text=f"Failed to generate {summary_type.value} summary: {e}",
                    summary_type=summary_type,
                    model_used=self.model
                )
        
        return results
    
    def is_available(self) -> bool:
        """Check if OpenAI API is available."""
        try:
            # Test API connection
            models = self.client.models.list()
            return True
        except Exception as e:
            logger.warning(f"OpenAI API not available: {e}")
            return False
    
    def estimate_cost(self, text: str, summary_type: SummaryType = SummaryType.BRIEF) -> dict:
        """Estimate the cost of summarizing text.
        
        Args:
            text: Text to summarize
            summary_type: Type of summary
            
        Returns:
            Dictionary with cost estimation
        """
        # Estimate input tokens
        input_tokens = self.estimate_tokens(text)
        
        # Add prompt tokens (rough estimate)
        prompt_template = self.get_prompt_template(summary_type)
        prompt_tokens = self.estimate_tokens(prompt_template)
        total_input_tokens = input_tokens + prompt_tokens
        
        # Estimate output tokens
        output_tokens = min(self.max_tokens, total_input_tokens // 4)  # Rough estimate
        
        # Pricing (as of 2024, varies by model)
        pricing = {
            "gpt-4": {"input": 0.03, "output": 0.06},  # per 1K tokens
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
        }
        
        model_pricing = pricing.get(self.model, pricing["gpt-4"])
        
        input_cost = (total_input_tokens / 1000) * model_pricing["input"]
        output_cost = (output_tokens / 1000) * model_pricing["output"]
        total_cost = input_cost + output_cost
        
        return {
            "input_tokens": total_input_tokens,
            "output_tokens": output_tokens,
            "input_cost_usd": input_cost,
            "output_cost_usd": output_cost,
            "total_cost_usd": total_cost,
            "model": self.model,
        }
