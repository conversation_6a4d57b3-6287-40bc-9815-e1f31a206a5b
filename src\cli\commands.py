"""CLI commands for the video processing system."""

import sys
from pathlib import Path
from typing import Optional, List

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from loguru import logger

from ..core.config import get_config
from ..downloaders.manager import DownloadManager
from ..transcription.manager import TranscriptionManager
from ..summarization.manager import SummarizationManager
from ..summarization.base import SummaryType
from ..utils.audio import AudioProcessor

console = Console()


@click.group()
@click.option('--config', '-c', help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(config: Optional[str], verbose: bool):
    """TikTok Video Processing System - Download, transcribe, and summarize videos."""
    # Configure logging
    log_level = "DEBUG" if verbose else "INFO"
    logger.remove()
    logger.add(sys.stderr, level=log_level, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # Load configuration
    if config:
        from ..core.config import ConfigManager
        config_manager = ConfigManager(config)
        config_manager.load_config()


@cli.command()
@click.argument('url')
@click.option('--output-dir', '-o', help='Output directory for all files')
@click.option('--whisper-local', is_flag=True, help='Use local Whisper model')
@click.option('--whisper-model', default='base', help='Whisper model size (tiny, base, small, medium, large)')
@click.option('--summary-type', type=click.Choice(['brief', 'detailed', 'keywords', 'qa']), default='brief', help='Type of summary to generate')
@click.option('--custom-prompt', help='Custom prompt for summarization')
@click.option('--keep-video', is_flag=True, help='Keep downloaded video file')
@click.option('--skip-download', is_flag=True, help='Skip download (use existing files)')
def process(
    url: str,
    output_dir: Optional[str],
    whisper_local: bool,
    whisper_model: str,
    summary_type: str,
    custom_prompt: Optional[str],
    keep_video: bool,
    skip_download: bool
):
    """Process a video: download, transcribe, and summarize."""
    try:
        config = get_config()
        
        # Set output directory
        if output_dir:
            base_output_dir = Path(output_dir)
        else:
            base_output_dir = Path("./output")
        
        base_output_dir.mkdir(parents=True, exist_ok=True)
        
        console.print(Panel.fit(
            f"[bold blue]Processing Video[/bold blue]\n"
            f"URL: {url}\n"
            f"Output: {base_output_dir}",
            border_style="blue"
        ))
        
        # Initialize managers
        download_manager = DownloadManager(str(base_output_dir / "downloads"))
        transcription_manager = TranscriptionManager()
        summarization_manager = SummarizationManager()
        audio_processor = AudioProcessor()
        
        video_path = None
        audio_path = None
        
        # Step 1: Download video
        if not skip_download:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=console
            ) as progress:
                download_task = progress.add_task("Downloading video...", total=None)
                
                try:
                    download_result = download_manager.download(url, extract_audio=True)
                    
                    if not download_result.success:
                        console.print(f"[red]Download failed: {download_result.error_message}[/red]")
                        return
                    
                    video_path = download_result.video_path
                    audio_path = download_result.audio_path
                    
                    progress.update(download_task, description="✓ Video downloaded", completed=True)
                    
                    # Display video info
                    if download_result.video_info:
                        info_table = Table(title="Video Information")
                        info_table.add_column("Property", style="cyan")
                        info_table.add_column("Value", style="white")
                        
                        info_table.add_row("Title", download_result.video_info.title)
                        info_table.add_row("Platform", download_result.video_info.platform.value)
                        if download_result.video_info.duration:
                            info_table.add_row("Duration", f"{download_result.video_info.duration:.1f}s")
                        if download_result.video_info.uploader:
                            info_table.add_row("Uploader", download_result.video_info.uploader)
                        
                        console.print(info_table)
                
                except Exception as e:
                    progress.update(download_task, description=f"✗ Download failed: {e}")
                    console.print(f"[red]Download error: {e}[/red]")
                    return
        else:
            console.print("[yellow]Skipping download - looking for existing files...[/yellow]")
            # Look for existing files in output directory
            # This would need implementation based on your file naming convention
        
        # Step 2: Extract/prepare audio if needed
        if video_path and not audio_path:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                audio_task = progress.add_task("Extracting audio...", total=None)
                
                try:
                    audio_path = audio_processor.extract_audio_from_video(video_path)
                    progress.update(audio_task, description="✓ Audio extracted")
                except Exception as e:
                    progress.update(audio_task, description=f"✗ Audio extraction failed: {e}")
                    console.print(f"[red]Audio extraction error: {e}[/red]")
                    return
        
        if not audio_path or not audio_path.exists():
            console.print("[red]No audio file available for transcription[/red]")
            return
        
        # Step 3: Transcribe audio
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            transcribe_task = progress.add_task("Transcribing audio...", total=None)
            
            try:
                transcription_result = transcription_manager.transcribe(
                    audio_path,
                    use_local=whisper_local,
                    output_dir=base_output_dir / "transcriptions"
                )
                
                progress.update(transcribe_task, description="✓ Transcription completed")
                
                # Display transcription info
                console.print(f"\n[green]Transcription completed![/green]")
                console.print(f"Language: {transcription_result.language or 'Unknown'}")
                console.print(f"Processing time: {transcription_result.processing_time:.2f}s")
                console.print(f"Text length: {len(transcription_result.text)} characters")
                
            except Exception as e:
                progress.update(transcribe_task, description=f"✗ Transcription failed: {e}")
                console.print(f"[red]Transcription error: {e}[/red]")
                return
        
        # Step 4: Generate summary
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            summary_task = progress.add_task("Generating summary...", total=None)
            
            try:
                summary_type_enum = SummaryType(summary_type)
                
                summary_result = summarization_manager.summarize(
                    transcription_result.text,
                    summary_type=summary_type_enum,
                    custom_prompt=custom_prompt,
                    output_file=base_output_dir / "summaries" / f"summary_{summary_type}.txt"
                )
                
                progress.update(summary_task, description="✓ Summary generated")
                
                # Display summary
                console.print(f"\n[green]Summary generated![/green]")
                console.print(f"Type: {summary_result.summary_type.value}")
                console.print(f"Compression ratio: {summary_result.compression_ratio:.2%}")
                console.print(f"Processing time: {summary_result.processing_time:.2f}s")
                
                # Show summary content
                summary_panel = Panel(
                    summary_result.summary,
                    title=f"{summary_type.title()} Summary",
                    border_style="green"
                )
                console.print(summary_panel)
                
            except Exception as e:
                progress.update(summary_task, description=f"✗ Summary generation failed: {e}")
                console.print(f"[red]Summary generation error: {e}[/red]")
                return
        
        # Cleanup
        if not keep_video and video_path and video_path.exists():
            video_path.unlink()
            console.print("[dim]Cleaned up video file[/dim]")
        
        console.print(f"\n[bold green]✓ Processing completed successfully![/bold green]")
        console.print(f"Output directory: {base_output_dir}")

    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        logger.exception("Unexpected error in process command")
        sys.exit(1)


@cli.command()
@click.argument('url')
@click.option('--output-dir', '-o', help='Output directory')
def download(url: str, output_dir: Optional[str]):
    """Download video only."""
    try:
        if output_dir:
            output_path = Path(output_dir)
        else:
            output_path = Path("./downloads")

        output_path.mkdir(parents=True, exist_ok=True)

        console.print(f"[blue]Downloading video from: {url}[/blue]")

        download_manager = DownloadManager(str(output_path))

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Downloading...", total=None)

            result = download_manager.download(url, extract_audio=False)

            if result.success:
                progress.update(task, description="✓ Download completed")
                console.print(f"[green]Video downloaded: {result.video_path}[/green]")
            else:
                progress.update(task, description="✗ Download failed")
                console.print(f"[red]Download failed: {result.error_message}[/red]")

    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('audio_path', type=click.Path(exists=True))
@click.option('--output-dir', '-o', help='Output directory')
@click.option('--local', is_flag=True, help='Use local Whisper model')
@click.option('--model', default='base', help='Whisper model size')
def transcribe(audio_path: str, output_dir: Optional[str], local: bool, model: str):
    """Transcribe audio file only."""
    try:
        audio_file = Path(audio_path)

        if output_dir:
            output_path = Path(output_dir)
        else:
            output_path = Path("./transcriptions")

        output_path.mkdir(parents=True, exist_ok=True)

        console.print(f"[blue]Transcribing: {audio_file.name}[/blue]")

        transcription_manager = TranscriptionManager()

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Transcribing...", total=None)

            result = transcription_manager.transcribe(
                audio_file,
                use_local=local,
                output_dir=output_path
            )

            progress.update(task, description="✓ Transcription completed")

            console.print(f"[green]Transcription completed![/green]")
            console.print(f"Language: {result.language or 'Unknown'}")
            console.print(f"Text length: {len(result.text)} characters")

            # Show preview
            preview = result.text[:200] + "..." if len(result.text) > 200 else result.text
            console.print(Panel(preview, title="Preview", border_style="blue"))

    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('text_file', type=click.Path(exists=True))
@click.option('--output-dir', '-o', help='Output directory')
@click.option('--type', 'summary_type', type=click.Choice(['brief', 'detailed', 'keywords', 'qa']), default='brief')
@click.option('--prompt', help='Custom prompt')
def summarize(text_file: str, output_dir: Optional[str], summary_type: str, prompt: Optional[str]):
    """Summarize text file only."""
    try:
        input_file = Path(text_file)

        if output_dir:
            output_path = Path(output_dir)
        else:
            output_path = Path("./summaries")

        output_path.mkdir(parents=True, exist_ok=True)

        # Read text file
        with open(input_file, 'r', encoding='utf-8') as f:
            text = f.read()

        console.print(f"[blue]Summarizing: {input_file.name}[/blue]")
        console.print(f"Text length: {len(text)} characters")

        summarization_manager = SummarizationManager()

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Generating summary...", total=None)

            summary_type_enum = SummaryType(summary_type)
            output_file = output_path / f"{input_file.stem}_summary_{summary_type}.txt"

            result = summarization_manager.summarize(
                text,
                summary_type=summary_type_enum,
                custom_prompt=prompt,
                output_file=output_file
            )

            progress.update(task, description="✓ Summary generated")

            console.print(f"[green]Summary generated![/green]")
            console.print(f"Compression ratio: {result.compression_ratio:.2%}")
            console.print(f"Output file: {output_file}")

            # Show summary
            summary_panel = Panel(
                result.summary,
                title=f"{summary_type.title()} Summary",
                border_style="green"
            )
            console.print(summary_panel)

    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
def info():
    """Show system information and configuration."""
    try:
        config = get_config()

        # System info table
        info_table = Table(title="System Information")
        info_table.add_column("Component", style="cyan")
        info_table.add_column("Status", style="white")
        info_table.add_column("Details", style="dim")

        # Check components
        download_manager = DownloadManager()
        transcription_manager = TranscriptionManager()
        summarization_manager = SummarizationManager()
        audio_processor = AudioProcessor()

        # Download capability
        info_table.add_row(
            "Video Download",
            "[green]✓ Available[/green]",
            "yt-dlp based downloader"
        )

        # FFmpeg
        ffmpeg_status = "[green]✓ Available[/green]" if audio_processor.check_ffmpeg_availability() else "[red]✗ Not found[/red]"
        info_table.add_row("FFmpeg", ffmpeg_status, "Required for audio processing")

        # Whisper
        transcriber_info = transcription_manager.get_transcriber_info()
        whisper_status = "[green]✓ Available[/green]" if transcriber_info['available'] else "[red]✗ Not available[/red]"
        whisper_details = f"Type: {transcriber_info['type']}, Model: {transcriber_info.get('model_size', 'N/A')}"
        info_table.add_row("Whisper", whisper_status, whisper_details)

        # GPT
        summarizer_info = summarization_manager.get_summarizer_info()
        gpt_status = "[green]✓ Available[/green]" if summarizer_info['available'] else "[red]✗ Not available[/red]"
        gpt_details = f"Model: {summarizer_info['model']}"
        info_table.add_row("GPT API", gpt_status, gpt_details)

        console.print(info_table)

        # Configuration
        config_table = Table(title="Configuration")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="white")

        config_table.add_row("Whisper Mode", "Local" if config.whisper.use_local else "API")
        config_table.add_row("Whisper Model", config.whisper.model_size)
        config_table.add_row("GPT Model", config.openai.model)
        config_table.add_row("Download Dir", config.download.output_dir)
        config_table.add_row("Transcription Dir", config.transcription.output_dir)
        config_table.add_row("Summary Dir", config.summarization.output_dir)

        console.print(config_table)

    except Exception as e:
        console.print(f"[red]Error getting system info: {e}[/red]")
        sys.exit(1)
