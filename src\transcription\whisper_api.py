"""OpenAI Whisper API transcription implementation."""

import time
from pathlib import Path
from typing import Optional
from loguru import logger

import openai
from openai import OpenAI

from .base import BaseTranscriber, TranscriptionResult, TranscriptionSegment


class WhisperAP<PERSON>ranscriber(BaseTranscriber):
    """OpenAI Whisper API transcriber."""
    
    def __init__(
        self,
        api_key: str,
        model: str = "whisper-1",
        language: Optional[str] = None,
        base_url: Optional[str] = None
    ):
        """Initialize Whisper API transcriber.
        
        Args:
            api_key: OpenAI API key
            model: Whisper model to use (whisper-1)
            language: Target language for transcription
            base_url: Custom API base URL
        """
        super().__init__(language)
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        
        # Initialize OpenAI client
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        logger.info(f"Initialized Whisper API transcriber with model {model}")
    
    def transcribe(self, audio_path: Path) -> TranscriptionResult:
        """Transcribe audio file using OpenAI Whisper API.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Transcription result
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        # Check file size (OpenAI has 25MB limit)
        file_size = audio_path.stat().st_size
        max_size = 25 * 1024 * 1024  # 25MB
        
        if file_size > max_size:
            raise Exception(f"Audio file too large ({file_size / 1024 / 1024:.1f}MB). "
                          f"OpenAI API limit is 25MB. Please split the file.")
        
        try:
            logger.info(f"Transcribing {audio_path.name} with OpenAI Whisper API")
            start_time = time.time()
            
            with open(audio_path, 'rb') as audio_file:
                # Call OpenAI Whisper API
                response = self.client.audio.transcriptions.create(
                    model=self.model,
                    file=audio_file,
                    language=self.language,
                    response_format="verbose_json",
                    timestamp_granularities=["segment"]
                )
            
            processing_time = time.time() - start_time
            logger.info(f"API transcription completed in {processing_time:.2f}s")
            
            # Convert API response to our format
            segments = []
            if hasattr(response, 'segments') and response.segments:
                for segment in response.segments:
                    segments.append(TranscriptionSegment(
                        start=segment.start,
                        end=segment.end,
                        text=segment.text,
                        confidence=getattr(segment, 'avg_logprob', None)
                    ))
            else:
                # If no segments, create one for the entire text
                segments.append(TranscriptionSegment(
                    start=0.0,
                    end=0.0,  # Unknown duration
                    text=response.text,
                    confidence=None
                ))
            
            return TranscriptionResult(
                text=response.text,
                segments=segments,
                language=getattr(response, 'language', None),
                processing_time=processing_time
            )
            
        except openai.APIError as e:
            error_msg = f"OpenAI API error: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"API transcription failed: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def transcribe_with_word_timestamps(self, audio_path: Path) -> TranscriptionResult:
        """Transcribe with word-level timestamps using OpenAI API.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Transcription result with word-level timestamps
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        try:
            logger.info(f"Transcribing {audio_path.name} with word timestamps via API")
            start_time = time.time()
            
            with open(audio_path, 'rb') as audio_file:
                response = self.client.audio.transcriptions.create(
                    model=self.model,
                    file=audio_file,
                    language=self.language,
                    response_format="verbose_json",
                    timestamp_granularities=["word", "segment"]
                )
            
            processing_time = time.time() - start_time
            
            # Convert with word-level detail if available
            segments = []
            if hasattr(response, 'segments') and response.segments:
                for segment in response.segments:
                    segments.append(TranscriptionSegment(
                        start=segment.start,
                        end=segment.end,
                        text=segment.text,
                        confidence=getattr(segment, 'avg_logprob', None)
                    ))
            else:
                # Fallback to single segment
                segments.append(TranscriptionSegment(
                    start=0.0,
                    end=0.0,
                    text=response.text,
                    confidence=None
                ))
            
            return TranscriptionResult(
                text=response.text,
                segments=segments,
                language=getattr(response, 'language', None),
                processing_time=processing_time
            )
            
        except Exception as e:
            error_msg = f"API transcription with word timestamps failed: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def is_available(self) -> bool:
        """Check if OpenAI API is available."""
        try:
            # Test API connection with a simple request
            models = self.client.models.list()
            return True
        except Exception as e:
            logger.warning(f"OpenAI API not available: {e}")
            return False
    
    def get_supported_formats(self) -> list[str]:
        """Get list of supported audio formats.
        
        Returns:
            List of supported file extensions
        """
        return [
            "mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm"
        ]
    
    def estimate_cost(self, audio_path: Path) -> dict:
        """Estimate the cost of transcribing an audio file.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Dictionary with cost estimation
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        # Get audio duration (approximate)
        try:
            import librosa
            duration = librosa.get_duration(path=str(audio_path))
        except:
            # Fallback: estimate from file size (very rough)
            file_size_mb = audio_path.stat().st_size / (1024 * 1024)
            duration = file_size_mb * 60  # Very rough estimate
        
        # OpenAI pricing (as of 2024): $0.006 per minute
        cost_per_minute = 0.006
        estimated_cost = (duration / 60) * cost_per_minute
        
        return {
            "duration_seconds": duration,
            "duration_minutes": duration / 60,
            "estimated_cost_usd": estimated_cost,
            "pricing_per_minute": cost_per_minute,
        }
