"""Base classes for text summarization services."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, Dict, Any
from enum import Enum


class SummaryType(Enum):
    """Types of summaries that can be generated."""
    BRIEF = "brief"
    DETAILED = "detailed"
    KEYWORDS = "keywords"
    QA = "qa"
    CUSTOM = "custom"


@dataclass
class SummaryResult:
    """Result of summarization operation."""
    summary: str
    summary_type: SummaryType
    original_length: int
    summary_length: int
    compression_ratio: float
    processing_time: Optional[float] = None
    model_used: Optional[str] = None
    prompt_used: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "summary": self.summary,
            "summary_type": self.summary_type.value,
            "original_length": self.original_length,
            "summary_length": self.summary_length,
            "compression_ratio": self.compression_ratio,
            "processing_time": self.processing_time,
            "model_used": self.model_used,
            "prompt_used": self.prompt_used,
        }
    
    @classmethod
    def from_text(
        cls,
        original_text: str,
        summary_text: str,
        summary_type: SummaryType,
        **kwargs
    ) -> "SummaryResult":
        """Create SummaryResult from text.
        
        Args:
            original_text: Original text that was summarized
            summary_text: Generated summary
            summary_type: Type of summary
            **kwargs: Additional fields
            
        Returns:
            SummaryResult instance
        """
        original_length = len(original_text)
        summary_length = len(summary_text)
        compression_ratio = summary_length / original_length if original_length > 0 else 0.0
        
        return cls(
            summary=summary_text,
            summary_type=summary_type,
            original_length=original_length,
            summary_length=summary_length,
            compression_ratio=compression_ratio,
            **kwargs
        )


class BaseSummarizer(ABC):
    """Abstract base class for text summarization services."""
    
    def __init__(self, model: str = "gpt-4"):
        """Initialize summarizer.
        
        Args:
            model: Model to use for summarization
        """
        self.model = model
    
    @abstractmethod
    def summarize(
        self,
        text: str,
        summary_type: SummaryType = SummaryType.BRIEF,
        custom_prompt: Optional[str] = None,
        **kwargs
    ) -> SummaryResult:
        """Summarize text.
        
        Args:
            text: Text to summarize
            summary_type: Type of summary to generate
            custom_prompt: Custom prompt for summarization
            **kwargs: Additional parameters
            
        Returns:
            Summary result
            
        Raises:
            Exception: If summarization fails
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the summarization service is available.
        
        Returns:
            True if service is available, False otherwise
        """
        pass
    
    def get_prompt_template(self, summary_type: SummaryType) -> str:
        """Get prompt template for summary type.
        
        Args:
            summary_type: Type of summary
            
        Returns:
            Prompt template string
        """
        templates = {
            SummaryType.BRIEF: """Please provide a brief summary of the following text in 3-5 bullet points. 
Focus on the main points and key insights.

Text: {text}

Summary:""",
            
            SummaryType.DETAILED: """Please provide a comprehensive and detailed summary of the following text. 
Include main themes, arguments, conclusions, and important details. Structure your response clearly.

Text: {text}

Detailed Summary:""",
            
            SummaryType.KEYWORDS: """Extract the key topics, themes, and important keywords from the following text. 
Present them as a list of keywords and phrases, organized by relevance.

Text: {text}

Keywords and Key Phrases:""",
            
            SummaryType.QA: """Based on the following text, generate 5-7 important questions and their answers. 
The questions should cover the main topics and key information presented.

Text: {text}

Questions and Answers:""",
        }
        
        return templates.get(summary_type, templates[SummaryType.BRIEF])
    
    def prepare_text(self, text: str, max_length: Optional[int] = None) -> str:
        """Prepare text for summarization.
        
        Args:
            text: Input text
            max_length: Maximum length to truncate to
            
        Returns:
            Prepared text
        """
        # Clean up text
        text = text.strip()
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        
        # Truncate if necessary
        if max_length and len(text) > max_length:
            text = text[:max_length] + "..."
        
        return text
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text.
        
        Args:
            text: Input text
            
        Returns:
            Estimated token count
        """
        # Rough estimation: 1 token ≈ 4 characters for English
        return len(text) // 4
    
    def split_long_text(self, text: str, max_tokens: int = 3000) -> list[str]:
        """Split long text into chunks for processing.
        
        Args:
            text: Input text
            max_tokens: Maximum tokens per chunk
            
        Returns:
            List of text chunks
        """
        max_chars = max_tokens * 4  # Rough conversion
        
        if len(text) <= max_chars:
            return [text]
        
        chunks = []
        sentences = text.split('. ')
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence) <= max_chars:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
