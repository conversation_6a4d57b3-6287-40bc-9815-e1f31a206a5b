"""Tests for configuration management."""

import pytest
import tempfile
import yaml
from pathlib import Path

from src.core.config import Config, Config<PERSON>ana<PERSON>, OpenAIConfig


class TestConfig:
    """Test configuration classes."""
    
    def test_openai_config_creation(self):
        """Test OpenAI configuration creation."""
        config = OpenAIConfig(api_key="test-key")
        assert config.api_key == "test-key"
        assert config.model == "gpt-4"
        assert config.temperature == 0.7
        assert config.max_tokens == 1000
    
    def test_config_creation_with_minimal_data(self):
        """Test Config creation with minimal required data."""
        config_data = {
            "openai": {"api_key": "test-key"}
        }
        config = Config(**config_data)
        assert config.openai.api_key == "test-key"
        assert config.whisper.use_local is True
        assert config.download.output_dir == "./downloads"


class TestConfigManager:
    """Test configuration manager."""
    
    def test_config_manager_initialization(self):
        """Test ConfigManager initialization."""
        manager = ConfigManager()
        assert manager.config_path == "config/config.yaml"
        assert manager._config is None
    
    def test_load_config_from_yaml(self):
        """Test loading configuration from YAML file."""
        # Create temporary config file
        config_data = {
            "openai": {
                "api_key": "test-key",
                "model": "gpt-3.5-turbo"
            },
            "whisper": {
                "use_local": False,
                "model_size": "small"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            manager = ConfigManager(temp_path)
            config = manager.load_config()
            
            assert config.openai.api_key == "test-key"
            assert config.openai.model == "gpt-3.5-turbo"
            assert config.whisper.use_local is False
            assert config.whisper.model_size == "small"
        finally:
            Path(temp_path).unlink()
    
    def test_env_override(self, monkeypatch):
        """Test environment variable overrides."""
        # Set environment variables
        monkeypatch.setenv("OPENAI_API_KEY", "env-key")
        monkeypatch.setenv("LOG_LEVEL", "DEBUG")
        
        # Create minimal config
        config_data = {"openai": {"api_key": "file-key"}}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            manager = ConfigManager(temp_path)
            config = manager.load_config()
            
            # Environment should override file
            assert config.openai.api_key == "env-key"
            assert config.logging.level == "DEBUG"
        finally:
            Path(temp_path).unlink()
    
    def test_missing_config_file(self):
        """Test handling of missing configuration file."""
        manager = ConfigManager("nonexistent.yaml")
        
        with pytest.raises(FileNotFoundError):
            manager.load_config()
    
    def test_invalid_yaml(self):
        """Test handling of invalid YAML."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_path = f.name
        
        try:
            manager = ConfigManager(temp_path)
            with pytest.raises(yaml.YAMLError):
                manager.load_config()
        finally:
            Path(temp_path).unlink()
    
    def test_config_caching(self):
        """Test that configuration is cached after first load."""
        config_data = {"openai": {"api_key": "test-key"}}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            manager = ConfigManager(temp_path)
            
            # First load
            config1 = manager.load_config()
            
            # Second load should return same instance
            config2 = manager.load_config()
            
            assert config1 is config2
        finally:
            Path(temp_path).unlink()
