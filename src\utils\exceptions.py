"""Custom exceptions for the video processing system."""


class VideoProcessingError(Exception):
    """Base exception for video processing errors."""
    pass


class DownloadError(VideoProcessingError):
    """Exception raised when video download fails."""
    
    def __init__(self, message: str, url: str = None, platform: str = None):
        super().__init__(message)
        self.url = url
        self.platform = platform


class UnsupportedPlatformError(DownloadError):
    """Exception raised when video platform is not supported."""
    
    def __init__(self, url: str, platform: str = None):
        message = f"Unsupported platform for URL: {url}"
        if platform:
            message += f" (detected: {platform})"
        super().__init__(message, url, platform)


class AudioExtractionError(VideoProcessingError):
    """Exception raised when audio extraction fails."""
    
    def __init__(self, message: str, video_path: str = None):
        super().__init__(message)
        self.video_path = video_path


class TranscriptionError(VideoProcessingError):
    """Exception raised when transcription fails."""
    
    def __init__(self, message: str, audio_path: str = None, service: str = None):
        super().__init__(message)
        self.audio_path = audio_path
        self.service = service


class WhisperModelError(TranscriptionError):
    """Exception raised when Whisper model loading fails."""
    
    def __init__(self, message: str, model_name: str = None):
        super().__init__(message)
        self.model_name = model_name


class SummarizationError(VideoProcessingError):
    """Exception raised when summarization fails."""
    
    def __init__(self, message: str, text_length: int = None, model: str = None):
        super().__init__(message)
        self.text_length = text_length
        self.model = model


class APIError(VideoProcessingError):
    """Exception raised when API calls fail."""
    
    def __init__(self, message: str, service: str = None, status_code: int = None):
        super().__init__(message)
        self.service = service
        self.status_code = status_code


class ConfigurationError(VideoProcessingError):
    """Exception raised when configuration is invalid."""
    
    def __init__(self, message: str, config_key: str = None):
        super().__init__(message)
        self.config_key = config_key


class ValidationError(VideoProcessingError):
    """Exception raised when input validation fails."""
    
    def __init__(self, message: str, field: str = None, value: str = None):
        super().__init__(message)
        self.field = field
        self.value = value


class DependencyError(VideoProcessingError):
    """Exception raised when required dependencies are missing."""
    
    def __init__(self, message: str, dependency: str = None):
        super().__init__(message)
        self.dependency = dependency


class FileSystemError(VideoProcessingError):
    """Exception raised when file system operations fail."""
    
    def __init__(self, message: str, path: str = None, operation: str = None):
        super().__init__(message)
        self.path = path
        self.operation = operation


class QuotaExceededError(APIError):
    """Exception raised when API quota is exceeded."""
    
    def __init__(self, message: str, service: str = None, reset_time: str = None):
        super().__init__(message, service)
        self.reset_time = reset_time


class AuthenticationError(APIError):
    """Exception raised when API authentication fails."""
    
    def __init__(self, message: str, service: str = None):
        super().__init__(message, service, 401)
