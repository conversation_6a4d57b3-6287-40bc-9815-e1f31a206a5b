"""Tests for validation utilities."""

import pytest
import tempfile
from pathlib import Path

from src.utils.validation import (
    validate_url,
    validate_video_url,
    validate_file_path,
    validate_audio_file,
    validate_video_file,
    validate_api_key,
    validate_whisper_model,
    validate_gpt_model,
    sanitize_filename,
    validate_output_directory
)


class TestURLValidation:
    """Test URL validation functions."""
    
    def test_validate_url_valid(self):
        """Test validation of valid URLs."""
        valid_urls = [
            "https://www.example.com",
            "http://example.com",
            "https://subdomain.example.com/path",
            "https://example.com:8080/path?query=value"
        ]
        
        for url in valid_urls:
            assert validate_url(url), f"Should be valid: {url}"
    
    def test_validate_url_invalid(self):
        """Test validation of invalid URLs."""
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",  # Wrong scheme
            "https://",  # No netloc
            "",
            None
        ]
        
        for url in invalid_urls:
            assert not validate_url(url), f"Should be invalid: {url}"
    
    def test_validate_video_url_bilibili(self):
        """Test validation of Bilibili URLs."""
        bilibili_urls = [
            "https://www.bilibili.com/video/BV1234567890",
            "https://bilibili.com/video/BV1234567890",
            "https://www.bilibili.com/bangumi/play/ep123456",
            "https://b23.tv/abc123"
        ]
        
        for url in bilibili_urls:
            assert validate_video_url(url), f"Should be valid Bilibili URL: {url}"
    
    def test_validate_video_url_douyin(self):
        """Test validation of Douyin URLs."""
        douyin_urls = [
            "https://www.douyin.com/video/1234567890",
            "https://v.douyin.com/abc123",
            "https://www.iesdouyin.com/share/video/1234567890"
        ]
        
        for url in douyin_urls:
            assert validate_video_url(url), f"Should be valid Douyin URL: {url}"
    
    def test_validate_video_url_unsupported(self):
        """Test validation of unsupported platform URLs."""
        unsupported_urls = [
            "https://www.youtube.com/watch?v=abc123",
            "https://www.tiktok.com/@user/video/123",
            "https://www.example.com/video.mp4"
        ]
        
        for url in unsupported_urls:
            assert not validate_video_url(url), f"Should be unsupported: {url}"


class TestFileValidation:
    """Test file validation functions."""
    
    def test_validate_file_path_existing(self):
        """Test validation of existing file paths."""
        with tempfile.NamedTemporaryFile() as temp_file:
            assert validate_file_path(temp_file.name, must_exist=True)
            assert validate_file_path(temp_file.name, must_exist=False)
    
    def test_validate_file_path_nonexistent(self):
        """Test validation of non-existent file paths."""
        nonexistent_path = "/tmp/nonexistent_file_12345.txt"
        assert not validate_file_path(nonexistent_path, must_exist=True)
        assert validate_file_path(nonexistent_path, must_exist=False)
    
    def test_validate_audio_file(self):
        """Test validation of audio files."""
        audio_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
        
        for ext in audio_extensions:
            with tempfile.NamedTemporaryFile(suffix=ext) as temp_file:
                assert validate_audio_file(temp_file.name)
    
    def test_validate_video_file(self):
        """Test validation of video files."""
        video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.webm']
        
        for ext in video_extensions:
            with tempfile.NamedTemporaryFile(suffix=ext) as temp_file:
                assert validate_video_file(temp_file.name)
    
    def test_validate_invalid_media_file(self):
        """Test validation of invalid media files."""
        with tempfile.NamedTemporaryFile(suffix='.txt') as temp_file:
            assert not validate_audio_file(temp_file.name)
            assert not validate_video_file(temp_file.name)


class TestAPIValidation:
    """Test API-related validation functions."""
    
    def test_validate_api_key_valid(self):
        """Test validation of valid API keys."""
        valid_keys = [
            "sk-1234567890abcdef1234567890abcdef1234567890abcdef123",
            "sk-abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
        ]
        
        for key in valid_keys:
            assert validate_api_key(key), f"Should be valid API key: {key}"
    
    def test_validate_api_key_invalid(self):
        """Test validation of invalid API keys."""
        invalid_keys = [
            "invalid-key",
            "sk-",
            "sk-short",
            "",
            None,
            123
        ]
        
        for key in invalid_keys:
            assert not validate_api_key(key), f"Should be invalid API key: {key}"
    
    def test_validate_whisper_model(self):
        """Test validation of Whisper model names."""
        valid_models = ['tiny', 'base', 'small', 'medium', 'large', 'large-v2', 'large-v3']
        
        for model in valid_models:
            assert validate_whisper_model(model), f"Should be valid Whisper model: {model}"
        
        invalid_models = ['invalid', 'gpt-4', '', None]
        for model in invalid_models:
            assert not validate_whisper_model(model), f"Should be invalid Whisper model: {model}"
    
    def test_validate_gpt_model(self):
        """Test validation of GPT model names."""
        valid_models = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4-32k']
        
        for model in valid_models:
            assert validate_gpt_model(model), f"Should be valid GPT model: {model}"
        
        invalid_models = ['invalid', 'whisper-1', '', None]
        for model in invalid_models:
            assert not validate_gpt_model(model), f"Should be invalid GPT model: {model}"


class TestFilenameValidation:
    """Test filename sanitization."""
    
    def test_sanitize_filename_invalid_chars(self):
        """Test sanitization of filenames with invalid characters."""
        test_cases = [
            ("file<name>.txt", "file_name_.txt"),
            ("file:name.txt", "file_name.txt"),
            ("file/name.txt", "file_name.txt"),
            ("file\\name.txt", "file_name.txt"),
            ("file|name.txt", "file_name.txt"),
            ("file?name.txt", "file_name.txt"),
            ("file*name.txt", "file_name.txt"),
            ('file"name.txt', "file_name.txt"),
        ]
        
        for original, expected in test_cases:
            result = sanitize_filename(original)
            assert result == expected, f"Expected {expected}, got {result}"
    
    def test_sanitize_filename_length_limit(self):
        """Test filename length limiting."""
        long_name = "a" * 300 + ".txt"
        result = sanitize_filename(long_name, max_length=50)
        assert len(result) <= 50
        assert result.endswith(".txt")
    
    def test_sanitize_filename_control_chars(self):
        """Test removal of control characters."""
        filename_with_control = "file\x00\x01name.txt"
        result = sanitize_filename(filename_with_control)
        assert "\x00" not in result
        assert "\x01" not in result


class TestDirectoryValidation:
    """Test directory validation."""
    
    def test_validate_output_directory_existing(self):
        """Test validation of existing directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            assert validate_output_directory(temp_dir, create=False)
            assert validate_output_directory(temp_dir, create=True)
    
    def test_validate_output_directory_create(self):
        """Test creation of non-existing directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            new_dir = Path(temp_dir) / "new_directory"
            assert not new_dir.exists()
            
            # Should create directory
            assert validate_output_directory(str(new_dir), create=True)
            assert new_dir.exists()
            assert new_dir.is_dir()
    
    def test_validate_output_directory_no_create(self):
        """Test validation without creating directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            new_dir = Path(temp_dir) / "new_directory"
            assert not new_dir.exists()
            
            # Should not create directory
            assert not validate_output_directory(str(new_dir), create=False)
            assert not new_dir.exists()
