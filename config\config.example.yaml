# TikTok Video Processing System Configuration

# OpenAI API Configuration
openai:
  api_key: "your-openai-api-key-here"
  model: "gpt-4"
  temperature: 0.7
  max_tokens: 1000

# Whisper Configuration
whisper:
  # Use local model (true) or OpenAI API (false)
  use_local: true
  # Model size: tiny, base, small, medium, large, large-v2, large-v3
  model_size: "base"
  # Language (auto-detect if null)
  language: null
  # Device: auto, cpu, cuda
  device: "auto"

# Download Configuration
download:
  # Output directory for downloaded videos
  output_dir: "./downloads"
  # Keep original video after processing
  keep_video: true
  # Video quality preference
  quality: "best"

# Transcription Configuration
transcription:
  # Output directory for transcriptions
  output_dir: "./transcriptions"
  # Output formats: txt, srt, vtt, json
  formats: ["txt", "srt"]
  # Include timestamps in text output
  include_timestamps: true

# Summarization Configuration
summarization:
  # Output directory for summaries
  output_dir: "./summaries"
  # Default prompt template
  default_prompt: |
    Please provide a comprehensive summary of the following video transcript.
    Include the main points, key insights, and any important details.
    Format the summary in a clear, structured manner.
  
  # Custom prompt templates
  prompts:
    brief: "Summarize the main points of this video in 3-5 bullet points."
    detailed: "Provide a detailed analysis of this video content, including main themes, arguments, and conclusions."
    keywords: "Extract the key topics, themes, and important keywords from this video transcript."
    qa: "Generate 5 important questions and answers based on this video content."

# Logging Configuration
logging:
  level: "INFO"
  file: "./logs/app.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Platform-specific settings
platforms:
  bilibili:
    # Add cookies file path if needed for private videos
    cookies: null
    # User agent string
    user_agent: null
  
  douyin:
    # Add cookies file path if needed
    cookies: null
    # User agent string
    user_agent: null
