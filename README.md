# TikTok Video Processing System

A comprehensive end-to-end audio/video processing system that downloads videos from Bilibili and Douyin (TikTok China), transcribes them using OpenAI Whisper, and generates AI-powered summaries using GPT.

## Features

- 📥 **Video Download**: Supports Bilibili and Douyin video downloads with automatic platform detection
- 🎙️ **Speech Transcription**: Uses OpenAI Whisper for accurate speech-to-text conversion
- 📄 **AI Summarization**: Leverages GPT API for intelligent content summarization
- 💻 **CLI Interface**: Easy-to-use command-line interface with progress indicators
- ⚙️ **Flexible Configuration**: Support for local and remote AI models

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tik
```

2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

3. Install FFmpeg (required for audio processing):
   - **Windows**: Download from https://ffmpeg.org/download.html
   - **macOS**: `brew install ffmpeg`
   - **Linux**: `sudo apt install ffmpeg` or `sudo yum install ffmpeg`

4. Set up configuration:
```bash
cp config/config.example.yaml config/config.yaml
# Edit config.yaml with your API keys and preferences
```

## Quick Start

1. **Basic video processing**:
```bash
python main.py process "https://www.bilibili.com/video/BV1234567890"
```

2. **Custom summarization prompt**:
```bash
python main.py process "https://v.douyin.com/abc123" --prompt "Summarize the main points in bullet format"
```

3. **Use local Whisper model**:
```bash
python main.py process "video_url" --whisper-local --model large
```

## Configuration

Edit `config/config.yaml` to customize:
- OpenAI API key
- Whisper model preferences
- Output directories
- Default prompts

## Project Structure

```
tik/
├── src/
│   ├── core/           # Core business logic
│   ├── downloaders/    # Video download modules
│   ├── transcription/  # Whisper integration
│   ├── summarization/  # GPT integration
│   └── utils/          # Utility functions
├── config/             # Configuration files
├── tests/              # Unit and integration tests
├── docs/               # Documentation
└── main.py            # CLI entry point
```

## License

MIT License
