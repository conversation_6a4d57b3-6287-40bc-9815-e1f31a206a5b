# 🎬 TikTok Video Processing System

A comprehensive end-to-end audio/video processing system that downloads videos from Bilibili and Do<PERSON>in (TikTok China), transcribes them using OpenAI Whisper, and generates AI-powered summaries using GPT.

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## ✨ Features

### 📥 Video Download Module
- **Multi-platform support**: Bilibili and Douyin (TikTok China)
- **Automatic platform detection** from URLs
- **High-quality downloads** with configurable quality settings
- **Audio extraction** for transcription processing
- **Batch processing** support for multiple videos

### 🎙️ Speech Transcription
- **OpenAI Whisper integration** (local and API)
- **Multiple model sizes**: tiny, base, small, medium, large, large-v3
- **GPU acceleration** support for faster local processing
- **Multiple output formats**: TXT, SRT, VTT, JSON
- **Language detection** and forced language options
- **Timestamp-accurate** transcriptions

### 📄 AI-Powered Summarization
- **GPT integration** (GPT-3.5, GPT-4, GPT-4-turbo)
- **Multiple summary types**:
  - 📝 Brief summaries
  - 📋 Detailed analysis
  - 🔑 Keyword extraction
  - ❓ Q&A generation
- **Custom prompts** for specialized content
- **Configurable templates** for different use cases
- **Cost estimation** for API usage

### 💻 User Interface
- **Rich CLI interface** with progress indicators
- **Colored output** and formatted results
- **Verbose logging** for debugging
- **System information** and health checks
- **Modular commands** for individual processing steps

### ⚙️ Advanced Configuration
- **YAML-based configuration** with environment variable overrides
- **Flexible output directories** and file organization
- **API key management** with multiple configuration methods
- **Error handling** with user-friendly messages and suggestions
- **Extensible architecture** for adding new platforms and services

## 🚀 Quick Start

### Automated Installation

**Linux/macOS:**
```bash
git clone <repository-url>
cd tik
chmod +x scripts/install.sh
./scripts/install.sh
```

**Windows:**
```cmd
git clone <repository-url>
cd tik
scripts\install.bat
```

### Manual Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd tik
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Install FFmpeg**:
   - **Windows**: Download from [ffmpeg.org](https://ffmpeg.org/download.html)
   - **macOS**: `brew install ffmpeg`
   - **Linux**: `sudo apt install ffmpeg`

3. **Configure**:
```bash
cp config/config.example.yaml config/config.yaml
cp .env.example .env
# Edit config.yaml and .env with your API keys
```

4. **Verify installation**:
```bash
python main.py info
```

## 📖 Usage Examples

### Basic Processing
```bash
# Process a Bilibili video with default settings
python main.py process "https://www.bilibili.com/video/BV1234567890"

# Process a Douyin video with custom output directory
python main.py process "https://v.douyin.com/abc123" --output-dir "./my_project"
```

### Advanced Options
```bash
# Use large Whisper model for better accuracy
python main.py process "video_url" --whisper-local --whisper-model large

# Generate detailed summary with custom prompt
python main.py process "video_url" --summary-type detailed --custom-prompt "Focus on technical details"

# Use OpenAI API for transcription (faster for single videos)
python main.py process "video_url" --no-whisper-local
```

### Individual Commands
```bash
# Download only
python main.py download "video_url" --output-dir "./downloads"

# Transcribe existing audio
python main.py transcribe "audio.wav" --local --model base

# Summarize existing text
python main.py summarize "transcript.txt" --type keywords
```

### System Information
```bash
# Check system status and configuration
python main.py info

# Enable verbose logging
python main.py -v process "video_url"
```

## 📁 Project Structure

```
tik/
├── 📂 src/                     # Source code
│   ├── 🧠 core/               # Core business logic
│   │   ├── config.py          # Configuration management
│   │   └── pipeline.py        # Main processing pipeline
│   ├── 📥 downloaders/        # Video download modules
│   │   ├── base.py           # Abstract downloader interface
│   │   ├── yt_dlp_downloader.py  # yt-dlp implementation
│   │   └── manager.py        # Download coordination
│   ├── 🎙️ transcription/      # Speech-to-text processing
│   │   ├── base.py           # Abstract transcriber interface
│   │   ├── whisper_local.py  # Local Whisper implementation
│   │   ├── whisper_api.py    # OpenAI API implementation
│   │   └── manager.py        # Transcription coordination
│   ├── 📄 summarization/      # AI summarization
│   │   ├── base.py           # Abstract summarizer interface
│   │   ├── gpt_summarizer.py # GPT implementation
│   │   └── manager.py        # Summarization coordination
│   ├── 💻 cli/                # Command-line interface
│   │   └── commands.py       # CLI commands and UI
│   └── 🛠️ utils/              # Utility functions
│       ├── audio.py          # Audio processing utilities
│       ├── validation.py     # Input validation
│       ├── exceptions.py     # Custom exceptions
│       ├── error_handler.py  # Error handling and messages
│       └── logging.py        # Logging configuration
├── 📂 config/                 # Configuration files
│   ├── config.example.yaml   # Example configuration
│   └── config.yaml          # User configuration (created)
├── 📂 tests/                  # Test suite
│   ├── test_config.py        # Configuration tests
│   └── test_validation.py    # Validation tests
├── 📂 docs/                   # Documentation
│   ├── INSTALLATION.md       # Installation guide
│   ├── USAGE.md             # Usage examples
│   ├── API.md               # API documentation
│   └── FAQ.md               # Frequently asked questions
├── 📂 examples/               # Example scripts
│   └── example_usage.py      # API usage examples
├── 📂 scripts/                # Installation scripts
│   ├── install.sh           # Linux/macOS installer
│   └── install.bat          # Windows installer
├── 🐍 main.py                 # CLI entry point
├── 📋 requirements.txt        # Python dependencies
├── ⚙️ setup.py               # Package setup
├── 🧪 pytest.ini             # Test configuration
├── 📄 LICENSE                 # MIT license
└── 📖 README.md              # This file
```

## 🔧 Configuration

### Basic Configuration (config/config.yaml)
```yaml
# OpenAI API settings
openai:
  api_key: "your-openai-api-key-here"
  model: "gpt-4"
  temperature: 0.7
  max_tokens: 1000

# Whisper transcription settings
whisper:
  use_local: true              # Use local model vs API
  model_size: "base"           # tiny, base, small, medium, large
  language: null               # Auto-detect or specify (e.g., "zh", "en")
  device: "auto"               # auto, cpu, cuda

# Output directories
download:
  output_dir: "./downloads"
  keep_video: true
  quality: "best"

transcription:
  output_dir: "./transcriptions"
  formats: ["txt", "srt"]
  include_timestamps: true

summarization:
  output_dir: "./summaries"
  default_prompt: "Please provide a comprehensive summary..."
```

### Environment Variables (.env)
```bash
# API Keys
OPENAI_API_KEY=your-openai-api-key-here

# Output Directories
DOWNLOAD_DIR=./downloads
TRANSCRIPTION_DIR=./transcriptions
SUMMARY_DIR=./summaries

# Logging
LOG_LEVEL=INFO
LOG_DIR=./logs
```

## 🎯 Use Cases

### 📚 Educational Content
- **Lecture transcription** and summarization
- **Tutorial analysis** with key point extraction
- **Course material** processing and organization

### 📰 News and Media
- **News video** summarization
- **Interview transcription** with Q&A generation
- **Content analysis** for research

### 💼 Business Applications
- **Meeting recordings** processing
- **Training material** analysis
- **Market research** from video content

### 🔬 Research and Analysis
- **Academic content** processing
- **Data collection** from video sources
- **Content categorization** and analysis

## 🛠️ Advanced Features

### GPU Acceleration
```bash
# Install CUDA-enabled PyTorch for faster local Whisper
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118

# Verify GPU availability
python -c "import torch; print(torch.cuda.is_available())"
```

### Custom Prompts
```yaml
# Define custom prompts in config.yaml
summarization:
  prompts:
    technical: "Focus on technical aspects and provide detailed explanations"
    marketing: "Analyze from a marketing perspective"
    educational: "Structure as educational content with learning objectives"
```

### Batch Processing
```bash
# Process multiple videos
for url in "url1" "url2" "url3"; do
    python main.py process "$url"
done
```

### API Integration
```python
# Use as a Python library
from src.core.pipeline import VideoProcessingPipeline
from src.summarization.base import SummaryType

pipeline = VideoProcessingPipeline("./output")
result = pipeline.process_video(
    url="https://www.bilibili.com/video/BV123",
    summary_type=SummaryType.DETAILED,
    use_local_whisper=True
)
```

## 📊 Performance and Costs

### Processing Speed
- **Local Whisper**: Depends on hardware (GPU recommended)
  - Tiny model: ~10x real-time
  - Base model: ~5x real-time
  - Large model: ~1x real-time
- **API Whisper**: ~2-5x real-time (network dependent)
- **GPT Summarization**: ~10-30 seconds per summary

### API Costs (Approximate)
- **Whisper API**: $0.006 per minute of audio
- **GPT-3.5-turbo**: $0.002 per 1K tokens
- **GPT-4**: $0.03 per 1K tokens (input)

### Hardware Requirements
- **Minimum**: 4GB RAM, CPU-only processing
- **Recommended**: 8GB+ RAM, NVIDIA GPU with 4GB+ VRAM
- **Storage**: ~1GB per hour of video content

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork** the repository
2. **Create** a feature branch
3. **Add** tests for new functionality
4. **Ensure** code follows black formatting
5. **Submit** a pull request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements.txt
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black src/ tests/

# Type checking
mypy src/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for Whisper and GPT APIs
- **yt-dlp** for video downloading capabilities
- **FFmpeg** for audio processing
- **Rich** for beautiful CLI interfaces

## 📞 Support

- 📖 **Documentation**: See [docs/](docs/) directory
- ❓ **FAQ**: Check [docs/FAQ.md](docs/FAQ.md)
- 🐛 **Issues**: Report bugs on GitHub Issues
- 💬 **Discussions**: Use GitHub Discussions for questions

---

**Made with ❤️ for the video processing community**
