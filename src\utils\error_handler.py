"""Error handling utilities for the video processing system."""

import traceback
from typing import Op<PERSON>, Dict, Any
from loguru import logger

from .exceptions import (
    VideoProcessingError,
    DownloadError,
    UnsupportedPlatformError,
    AudioExtractionError,
    TranscriptionError,
    WhisperModelError,
    SummarizationError,
    APIError,
    ConfigurationError,
    ValidationError,
    DependencyError,
    FileSystemError,
    QuotaExceededError,
    AuthenticationError,
)


class ErrorHandler:
    """Centralized error handling and user-friendly error messages."""
    
    def __init__(self, verbose: bool = False):
        """Initialize error handler.
        
        Args:
            verbose: Whether to show detailed error information
        """
        self.verbose = verbose
    
    def handle_error(self, error: Exception, context: Optional[str] = None) -> Dict[str, Any]:
        """Handle an error and return user-friendly information.
        
        Args:
            error: Exception to handle
            context: Additional context about where the error occurred
            
        Returns:
            Dictionary with error information
        """
        error_info = {
            "type": type(error).__name__,
            "message": str(error),
            "user_message": self._get_user_friendly_message(error),
            "suggestions": self._get_suggestions(error),
            "context": context,
        }
        
        if self.verbose:
            error_info["traceback"] = traceback.format_exc()
        
        # Log the error
        self._log_error(error, context)
        
        return error_info
    
    def _get_user_friendly_message(self, error: Exception) -> str:
        """Get user-friendly error message.
        
        Args:
            error: Exception to process
            
        Returns:
            User-friendly error message
        """
        if isinstance(error, UnsupportedPlatformError):
            return f"The video platform is not supported. Currently supported: Bilibili, Douyin."
        
        elif isinstance(error, DownloadError):
            return f"Failed to download video. This could be due to network issues, private videos, or platform restrictions."
        
        elif isinstance(error, AudioExtractionError):
            return f"Failed to extract audio from video. Please ensure FFmpeg is installed and the video file is valid."
        
        elif isinstance(error, WhisperModelError):
            return f"Failed to load Whisper model. Try using a smaller model or check your internet connection."
        
        elif isinstance(error, TranscriptionError):
            return f"Failed to transcribe audio. The audio might be too long, corrupted, or in an unsupported format."
        
        elif isinstance(error, AuthenticationError):
            return f"API authentication failed. Please check your API key and ensure it's valid."
        
        elif isinstance(error, QuotaExceededError):
            return f"API quota exceeded. Please wait before making more requests or upgrade your plan."
        
        elif isinstance(error, SummarizationError):
            return f"Failed to generate summary. The text might be too long or the API might be unavailable."
        
        elif isinstance(error, ConfigurationError):
            return f"Configuration error. Please check your config file and ensure all required settings are provided."
        
        elif isinstance(error, ValidationError):
            return f"Invalid input: {error.message}"
        
        elif isinstance(error, DependencyError):
            return f"Missing required dependency. Please install the required software and try again."
        
        elif isinstance(error, FileSystemError):
            return f"File system error. Please check file permissions and available disk space."
        
        elif isinstance(error, APIError):
            return f"API error occurred. Please check your internet connection and API settings."
        
        elif isinstance(error, VideoProcessingError):
            return f"Video processing error: {error}"
        
        else:
            return f"An unexpected error occurred: {error}"
    
    def _get_suggestions(self, error: Exception) -> list[str]:
        """Get suggestions for fixing the error.
        
        Args:
            error: Exception to process
            
        Returns:
            List of suggestions
        """
        suggestions = []
        
        if isinstance(error, UnsupportedPlatformError):
            suggestions.extend([
                "Check if the URL is correct and accessible",
                "Try using a different video URL from Bilibili or Douyin",
                "Ensure the video is public and not region-restricted"
            ])
        
        elif isinstance(error, DownloadError):
            suggestions.extend([
                "Check your internet connection",
                "Verify the video URL is correct and accessible",
                "Try again later if the platform is experiencing issues",
                "Check if the video requires authentication (cookies)"
            ])
        
        elif isinstance(error, AudioExtractionError):
            suggestions.extend([
                "Install FFmpeg: https://ffmpeg.org/download.html",
                "Ensure the video file is not corrupted",
                "Try with a different video format",
                "Check available disk space"
            ])
        
        elif isinstance(error, WhisperModelError):
            suggestions.extend([
                "Try using a smaller Whisper model (e.g., 'tiny' or 'base')",
                "Check your internet connection for model download",
                "Ensure you have enough RAM for the model",
                "Try using the API version instead of local model"
            ])
        
        elif isinstance(error, TranscriptionError):
            suggestions.extend([
                "Try splitting long audio files into smaller chunks",
                "Ensure the audio file is in a supported format (WAV, MP3, M4A)",
                "Check if the audio file is corrupted",
                "Try using a different Whisper model"
            ])
        
        elif isinstance(error, AuthenticationError):
            suggestions.extend([
                "Check your OpenAI API key in the configuration",
                "Ensure the API key is valid and not expired",
                "Verify you have sufficient API credits",
                "Check if the API key has the required permissions"
            ])
        
        elif isinstance(error, QuotaExceededError):
            suggestions.extend([
                "Wait for your quota to reset",
                "Upgrade your OpenAI plan for higher limits",
                "Use shorter text for summarization",
                "Try again later"
            ])
        
        elif isinstance(error, SummarizationError):
            suggestions.extend([
                "Try with shorter text",
                "Check your OpenAI API key and credits",
                "Use a different GPT model",
                "Split long text into smaller chunks"
            ])
        
        elif isinstance(error, ConfigurationError):
            suggestions.extend([
                "Check the configuration file format (YAML)",
                "Ensure all required fields are provided",
                "Copy from config.example.yaml if needed",
                "Verify API keys and paths are correct"
            ])
        
        elif isinstance(error, DependencyError):
            suggestions.extend([
                "Install missing dependencies with: pip install -r requirements.txt",
                "Check the installation guide in README.md",
                "Ensure you're using a compatible Python version",
                "Try installing dependencies individually"
            ])
        
        elif isinstance(error, FileSystemError):
            suggestions.extend([
                "Check file and directory permissions",
                "Ensure sufficient disk space is available",
                "Verify the file path is correct",
                "Try running with administrator privileges if needed"
            ])
        
        return suggestions
    
    def _log_error(self, error: Exception, context: Optional[str] = None):
        """Log error with appropriate level.
        
        Args:
            error: Exception to log
            context: Additional context
        """
        log_message = f"Error in {context}: {error}" if context else f"Error: {error}"
        
        if isinstance(error, (ValidationError, ConfigurationError)):
            logger.warning(log_message)
        elif isinstance(error, (APIError, QuotaExceededError, AuthenticationError)):
            logger.error(log_message)
        elif isinstance(error, VideoProcessingError):
            logger.error(log_message)
        else:
            logger.exception(log_message)
    
    def format_error_for_cli(self, error_info: Dict[str, Any]) -> str:
        """Format error information for CLI display.
        
        Args:
            error_info: Error information dictionary
            
        Returns:
            Formatted error message for CLI
        """
        lines = []
        
        # Main error message
        lines.append(f"❌ {error_info['user_message']}")
        
        # Context if available
        if error_info.get('context'):
            lines.append(f"📍 Context: {error_info['context']}")
        
        # Suggestions
        if error_info.get('suggestions'):
            lines.append("\n💡 Suggestions:")
            for suggestion in error_info['suggestions']:
                lines.append(f"   • {suggestion}")
        
        # Technical details if verbose
        if self.verbose and error_info.get('traceback'):
            lines.append(f"\n🔍 Technical details:")
            lines.append(f"   Error type: {error_info['type']}")
            lines.append(f"   Message: {error_info['message']}")
        
        return "\n".join(lines)
