"""Main processing pipeline for the video processing system."""

import time
from pathlib import Path
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from loguru import logger

from .config import get_config
from ..downloaders.manager import DownloadManager
from ..transcription.manager import TranscriptionManager
from ..summarization.manager import SummarizationManager
from ..summarization.base import SummaryType
from ..utils.audio import AudioProcessor
from ..utils.exceptions import VideoProcessingError
from ..utils.error_handler import <PERSON>rrorHandler


@dataclass
class ProcessingResult:
    """Result of the complete processing pipeline."""
    success: bool
    video_info: Optional[Dict[str, Any]] = None
    video_path: Optional[Path] = None
    audio_path: Optional[Path] = None
    transcription_text: Optional[str] = None
    transcription_files: Optional[List[Path]] = None
    summary_text: Optional[str] = None
    summary_file: Optional[Path] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "success": self.success,
            "video_info": self.video_info,
            "video_path": str(self.video_path) if self.video_path else None,
            "audio_path": str(self.audio_path) if self.audio_path else None,
            "transcription_text": self.transcription_text,
            "transcription_files": [str(f) for f in self.transcription_files] if self.transcription_files else None,
            "summary_text": self.summary_text,
            "summary_file": str(self.summary_file) if self.summary_file else None,
            "processing_time": self.processing_time,
            "error_message": self.error_message,
        }


class VideoProcessingPipeline:
    """Main processing pipeline for video processing."""
    
    def __init__(self, output_dir: Optional[str] = None):
        """Initialize processing pipeline.
        
        Args:
            output_dir: Base output directory for all files
        """
        self.config = get_config()
        self.error_handler = ErrorHandler(verbose=False)
        
        # Set up output directories
        if output_dir:
            self.base_output_dir = Path(output_dir)
        else:
            self.base_output_dir = Path("./output")
        
        self.base_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize managers
        self.download_manager = DownloadManager(str(self.base_output_dir / "downloads"))
        self.transcription_manager = TranscriptionManager()
        self.summarization_manager = SummarizationManager()
        self.audio_processor = AudioProcessor()
        
        logger.info(f"Initialized pipeline with output directory: {self.base_output_dir}")
    
    def process_video(
        self,
        url: str,
        summary_type: SummaryType = SummaryType.BRIEF,
        custom_prompt: Optional[str] = None,
        use_local_whisper: Optional[bool] = None,
        whisper_model: Optional[str] = None,
        keep_video: bool = False,
        skip_download: bool = False
    ) -> ProcessingResult:
        """Process a video through the complete pipeline.
        
        Args:
            url: Video URL to process
            summary_type: Type of summary to generate
            custom_prompt: Custom prompt for summarization
            use_local_whisper: Whether to use local Whisper model
            whisper_model: Whisper model size to use
            keep_video: Whether to keep the downloaded video file
            skip_download: Whether to skip download step
            
        Returns:
            Processing result
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting video processing pipeline for: {url}")
            
            # Step 1: Download video
            video_path = None
            audio_path = None
            video_info = None
            
            if not skip_download:
                logger.info("Step 1: Downloading video")
                download_result = self.download_manager.download(url, extract_audio=True)
                
                if not download_result.success:
                    return ProcessingResult(
                        success=False,
                        error_message=f"Download failed: {download_result.error_message}",
                        processing_time=time.time() - start_time
                    )
                
                video_path = download_result.video_path
                audio_path = download_result.audio_path
                video_info = download_result.video_info.to_dict() if download_result.video_info else None
                
                logger.info(f"Video downloaded: {video_path}")
                logger.info(f"Audio extracted: {audio_path}")
            else:
                logger.info("Skipping download step")
                # Would need to implement file discovery logic here
            
            # Step 2: Ensure audio is available
            if video_path and not audio_path:
                logger.info("Step 2: Extracting audio from video")
                audio_path = self.audio_processor.extract_audio_from_video(video_path)
                logger.info(f"Audio extracted: {audio_path}")
            
            if not audio_path or not audio_path.exists():
                return ProcessingResult(
                    success=False,
                    error_message="No audio file available for transcription",
                    video_info=video_info,
                    video_path=video_path,
                    processing_time=time.time() - start_time
                )
            
            # Step 3: Transcribe audio
            logger.info("Step 3: Transcribing audio")
            transcription_result = self.transcription_manager.transcribe(
                audio_path,
                use_local=use_local_whisper,
                output_dir=self.base_output_dir / "transcriptions"
            )
            
            transcription_files = self.transcription_manager.save_transcription_results(
                transcription_result,
                audio_path,
                self.base_output_dir / "transcriptions"
            )
            
            logger.info(f"Transcription completed: {len(transcription_result.text)} characters")
            
            # Step 4: Generate summary
            logger.info("Step 4: Generating summary")
            summary_file = self.base_output_dir / "summaries" / f"summary_{summary_type.value}.txt"
            
            summary_result = self.summarization_manager.summarize(
                transcription_result.text,
                summary_type=summary_type,
                custom_prompt=custom_prompt,
                output_file=summary_file
            )
            
            logger.info(f"Summary generated: {len(summary_result.summary)} characters")
            
            # Step 5: Cleanup
            if not keep_video and video_path and video_path.exists():
                video_path.unlink()
                logger.info("Cleaned up video file")
                video_path = None
            
            processing_time = time.time() - start_time
            logger.info(f"Pipeline completed successfully in {processing_time:.2f}s")
            
            return ProcessingResult(
                success=True,
                video_info=video_info,
                video_path=video_path,
                audio_path=audio_path,
                transcription_text=transcription_result.text,
                transcription_files=transcription_files,
                summary_text=summary_result.summary,
                summary_file=summary_file,
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_info = self.error_handler.handle_error(e, "video processing pipeline")
            
            logger.error(f"Pipeline failed: {error_info['user_message']}")
            
            return ProcessingResult(
                success=False,
                error_message=error_info['user_message'],
                processing_time=processing_time
            )
    
    def process_multiple_videos(
        self,
        urls: List[str],
        **kwargs
    ) -> List[ProcessingResult]:
        """Process multiple videos.
        
        Args:
            urls: List of video URLs to process
            **kwargs: Arguments passed to process_video
            
        Returns:
            List of processing results
        """
        results = []
        
        for i, url in enumerate(urls, 1):
            logger.info(f"Processing video {i}/{len(urls)}: {url}")
            
            try:
                result = self.process_video(url, **kwargs)
                results.append(result)
                
                if result.success:
                    logger.info(f"Video {i} processed successfully")
                else:
                    logger.error(f"Video {i} failed: {result.error_message}")
                    
            except Exception as e:
                error_info = self.error_handler.handle_error(e, f"video {i}")
                results.append(ProcessingResult(
                    success=False,
                    error_message=error_info['user_message']
                ))
        
        return results
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Get information about the pipeline and its components.
        
        Returns:
            Dictionary with pipeline information
        """
        return {
            "output_directory": str(self.base_output_dir),
            "download_manager": {
                "available": True,
                "output_dir": self.download_manager.output_dir
            },
            "transcription_manager": self.transcription_manager.get_transcriber_info(),
            "summarization_manager": self.summarization_manager.get_summarizer_info(),
            "audio_processor": {
                "ffmpeg_available": self.audio_processor.check_ffmpeg_availability()
            }
        }
