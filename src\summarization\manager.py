"""Summarization manager for coordinating text summarization services."""

from pathlib import Path
from typing import Optional, Dict, List
from loguru import logger

from ..core.config import get_config
from .base import BaseSummarizer, SummaryResult, SummaryType
from .gpt_summarizer import GPTSummarizer


class SummarizationManager:
    """Manager for coordinating text summarization services."""
    
    def __init__(self):
        """Initialize summarization manager."""
        self.config = get_config()
        self._summarizer: Optional[BaseSummarizer] = None
    
    def get_summarizer(self) -> GPTSummarizer:
        """Get the configured summarizer.
        
        Returns:
            Configured summarizer instance
        """
        if self._summarizer is None:
            logger.info("Initializing GPT summarizer")
            self._summarizer = GPTSummarizer(
                api_key=self.config.openai.api_key,
                model=self.config.openai.model,
                temperature=self.config.openai.temperature,
                max_tokens=self.config.openai.max_tokens
            )
        
        return self._summarizer
    
    def summarize(
        self,
        text: str,
        summary_type: SummaryType = SummaryType.BRIEF,
        custom_prompt: Optional[str] = None,
        output_file: Optional[Path] = None,
        **kwargs
    ) -> SummaryResult:
        """Summarize text and optionally save to file.
        
        Args:
            text: Text to summarize
            summary_type: Type of summary to generate
            custom_prompt: Custom prompt for summarization
            output_file: File to save summary to
            **kwargs: Additional parameters
            
        Returns:
            Summary result
        """
        if not text.strip():
            raise ValueError("Input text is empty")
        
        # Get summarizer
        summarizer = self.get_summarizer()
        
        # Check if summarizer is available
        if not summarizer.is_available():
            raise Exception("GPT summarizer is not available")
        
        # Generate summary
        logger.info(f"Starting {summary_type.value} summarization")
        result = summarizer.summarize(text, summary_type, custom_prompt, **kwargs)
        
        # Save to file if requested
        if output_file:
            self.save_summary(result, output_file)
        
        return result
    
    def summarize_with_custom_prompt(
        self,
        text: str,
        prompt_name: str,
        output_file: Optional[Path] = None,
        **kwargs
    ) -> SummaryResult:
        """Summarize using a predefined custom prompt.
        
        Args:
            text: Text to summarize
            prompt_name: Name of prompt from config
            output_file: File to save summary to
            **kwargs: Additional parameters
            
        Returns:
            Summary result
        """
        # Get custom prompt from config
        custom_prompts = self.config.summarization.prompts
        
        if prompt_name not in custom_prompts:
            available_prompts = list(custom_prompts.keys())
            raise ValueError(f"Prompt '{prompt_name}' not found. Available: {available_prompts}")
        
        custom_prompt = custom_prompts[prompt_name]
        
        return self.summarize(
            text,
            SummaryType.CUSTOM,
            custom_prompt,
            output_file,
            **kwargs
        )
    
    def generate_multiple_summaries(
        self,
        text: str,
        summary_types: Optional[List[SummaryType]] = None,
        output_dir: Optional[Path] = None,
        base_filename: str = "summary",
        **kwargs
    ) -> Dict[SummaryType, SummaryResult]:
        """Generate multiple types of summaries.
        
        Args:
            text: Text to summarize
            summary_types: List of summary types (default: all types)
            output_dir: Directory to save summaries
            base_filename: Base filename for saved summaries
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping summary types to results
        """
        if summary_types is None:
            summary_types = [
                SummaryType.BRIEF,
                SummaryType.DETAILED,
                SummaryType.KEYWORDS,
                SummaryType.QA
            ]
        
        summarizer = self.get_summarizer()
        results = summarizer.generate_multiple_summaries(text, summary_types, **kwargs)
        
        # Save results if output directory specified
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            for summary_type, result in results.items():
                filename = f"{base_filename}_{summary_type.value}.txt"
                output_file = output_dir / filename
                self.save_summary(result, output_file)
        
        return results
    
    def save_summary(self, result: SummaryResult, output_file: Path) -> Path:
        """Save summary result to file.
        
        Args:
            result: Summary result to save
            output_file: Output file path
            
        Returns:
            Path to saved file
        """
        output_file = Path(output_file)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create formatted content
        content = self._format_summary_for_file(result)
        
        # Save to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Summary saved to {output_file}")
        return output_file
    
    def _format_summary_for_file(self, result: SummaryResult) -> str:
        """Format summary result for file output.
        
        Args:
            result: Summary result
            
        Returns:
            Formatted content string
        """
        lines = []
        
        # Header
        lines.append(f"# {result.summary_type.value.title()} Summary")
        lines.append("")
        
        # Metadata
        lines.append("## Summary Information")
        lines.append(f"- **Type**: {result.summary_type.value}")
        lines.append(f"- **Original Length**: {result.original_length:,} characters")
        lines.append(f"- **Summary Length**: {result.summary_length:,} characters")
        lines.append(f"- **Compression Ratio**: {result.compression_ratio:.2%}")
        
        if result.model_used:
            lines.append(f"- **Model Used**: {result.model_used}")
        
        if result.processing_time:
            lines.append(f"- **Processing Time**: {result.processing_time:.2f} seconds")
        
        lines.append("")
        
        # Summary content
        lines.append("## Summary")
        lines.append("")
        lines.append(result.summary)
        
        # Prompt used (if available)
        if result.prompt_used:
            lines.append("")
            lines.append("## Prompt Used")
            lines.append("```")
            lines.append(result.prompt_used)
            lines.append("```")
        
        return "\n".join(lines)
    
    def get_summarizer_info(self) -> dict:
        """Get information about the current summarizer.
        
        Returns:
            Dictionary with summarizer information
        """
        summarizer = self.get_summarizer()
        
        return {
            "type": "gpt",
            "model": summarizer.model,
            "temperature": summarizer.temperature,
            "max_tokens": summarizer.max_tokens,
            "available": summarizer.is_available(),
        }
    
    def estimate_cost(self, text: str, summary_type: SummaryType = SummaryType.BRIEF) -> dict:
        """Estimate the cost of summarizing text.
        
        Args:
            text: Text to summarize
            summary_type: Type of summary
            
        Returns:
            Dictionary with cost estimation
        """
        summarizer = self.get_summarizer()
        return summarizer.estimate_cost(text, summary_type)
