# Installation Guide

This guide will help you install and set up the TikTok Video Processing System.

## Prerequisites

- Python 3.8 or higher
- FFmpeg (for audio processing)
- Git (for cloning the repository)

## Step 1: Clone the Repository

```bash
git clone <repository-url>
cd tik
```

## Step 2: Create Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

## Step 3: Install Python Dependencies

```bash
pip install -r requirements.txt
```

## Step 4: Install FFmpeg

FFmpeg is required for audio extraction and processing.

### Windows

1. Download FFmpeg from https://ffmpeg.org/download.html
2. Extract the archive
3. Add the `bin` folder to your system PATH
4. Verify installation: `ffmpeg -version`

### macOS

```bash
# Using Homebrew
brew install ffmpeg

# Using MacPorts
sudo port install ffmpeg
```

### Linux

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# CentOS/RHEL/Fedora
sudo yum install ffmpeg
# or
sudo dnf install ffmpeg

# Arch Linux
sudo pacman -S ffmpeg
```

## Step 5: Configuration

1. Copy the example configuration:
```bash
cp config/config.example.yaml config/config.yaml
```

2. Edit `config/config.yaml` with your settings:
```yaml
openai:
  api_key: "your-openai-api-key-here"
  model: "gpt-4"
  temperature: 0.7
  max_tokens: 1000

whisper:
  use_local: true
  model_size: "base"
  device: "auto"
```

3. (Optional) Set environment variables:
```bash
# Create .env file
cp .env.example .env

# Edit .env file with your API keys
OPENAI_API_KEY=your-openai-api-key-here
```

## Step 6: Verify Installation

Run the system information command to verify everything is working:

```bash
python main.py info
```

This should show the status of all components:
- ✅ Video Download: Available
- ✅ FFmpeg: Available  
- ✅ Whisper: Available
- ✅ GPT API: Available

## Optional: GPU Acceleration

For faster local Whisper transcription, you can install PyTorch with CUDA support:

```bash
# Check if CUDA is available
python -c "import torch; print(torch.cuda.is_available())"

# Install PyTorch with CUDA (adjust version as needed)
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## Troubleshooting

### Common Issues

1. **FFmpeg not found**
   - Ensure FFmpeg is installed and in your PATH
   - Try running `ffmpeg -version` in terminal

2. **OpenAI API errors**
   - Verify your API key is correct
   - Check your OpenAI account has sufficient credits
   - Ensure the API key has the required permissions

3. **Whisper model download fails**
   - Check your internet connection
   - Try using a smaller model (e.g., "tiny" or "base")
   - Consider using the API version instead

4. **Permission errors**
   - Ensure you have write permissions to the output directories
   - Try running with administrator privileges if needed

5. **Import errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check you're using the correct Python version (3.8+)

### Getting Help

If you encounter issues:

1. Check the logs in `logs/app.log`
2. Run with verbose mode: `python main.py -v <command>`
3. Check the [FAQ](FAQ.md) for common solutions
4. Open an issue on GitHub with error details

## Next Steps

Once installation is complete, see the [Usage Guide](USAGE.md) for examples of how to use the system.
