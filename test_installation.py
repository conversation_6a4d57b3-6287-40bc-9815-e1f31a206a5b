#!/usr/bin/env python3
"""
Installation test script for the TikTok Video Processing System.
This script verifies that all components are properly installed and configured.
"""

import sys
import subprocess
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} (compatible)")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (requires 3.8+)")
        return False

def test_imports():
    """Test importing core modules."""
    print("📦 Testing module imports...")
    
    modules_to_test = [
        ("click", "CLI framework"),
        ("rich", "Rich text and beautiful formatting"),
        ("loguru", "Logging"),
        ("pydantic", "Data validation"),
        ("yaml", "YAML configuration"),
        ("requests", "HTTP requests"),
        ("yt_dlp", "Video downloading"),
        ("openai", "OpenAI API"),
        ("whisper", "OpenAI Whisper"),
        ("ffmpeg", "FFmpeg Python bindings"),
        ("torch", "PyTorch"),
        ("librosa", "Audio processing"),
        ("soundfile", "Audio file I/O"),
    ]
    
    failed_imports = []
    
    for module, description in modules_to_test:
        try:
            __import__(module)
            print(f"   ✅ {module} ({description})")
        except ImportError as e:
            print(f"   ❌ {module} ({description}) - {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0, failed_imports

def test_ffmpeg():
    """Test FFmpeg availability."""
    print("🎵 Testing FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✅ {version_line}")
            return True
        else:
            print(f"   ❌ FFmpeg returned error code {result.returncode}")
            return False
    except FileNotFoundError:
        print("   ❌ FFmpeg not found in PATH")
        return False
    except subprocess.TimeoutExpired:
        print("   ❌ FFmpeg command timed out")
        return False
    except Exception as e:
        print(f"   ❌ Error testing FFmpeg: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("⚙️ Testing configuration...")
    try:
        from src.core.config import ConfigManager
        
        # Test loading example configuration
        example_config_path = Path("config/config.example.yaml")
        if example_config_path.exists():
            manager = ConfigManager(str(example_config_path))
            config = manager.load_config()
            print("   ✅ Example configuration loaded successfully")
            
            # Test actual configuration if it exists
            actual_config_path = Path("config/config.yaml")
            if actual_config_path.exists():
                manager = ConfigManager(str(actual_config_path))
                config = manager.load_config()
                print("   ✅ User configuration loaded successfully")
                
                # Check if API key is configured
                if config.openai.api_key and config.openai.api_key != "your-openai-api-key-here":
                    print("   ✅ OpenAI API key configured")
                else:
                    print("   ⚠️  OpenAI API key not configured (edit config/config.yaml)")
            else:
                print("   ⚠️  User configuration not found (copy from config.example.yaml)")
            
            return True
        else:
            print("   ❌ Example configuration not found")
            return False
            
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def test_core_functionality():
    """Test core system functionality."""
    print("🧪 Testing core functionality...")
    try:
        # Test downloaders
        from src.downloaders.manager import DownloadManager
        download_manager = DownloadManager()
        print("   ✅ Download manager initialized")
        
        # Test transcription
        from src.transcription.manager import TranscriptionManager
        transcription_manager = TranscriptionManager()
        print("   ✅ Transcription manager initialized")
        
        # Test summarization
        from src.summarization.manager import SummarizationManager
        summarization_manager = SummarizationManager()
        print("   ✅ Summarization manager initialized")
        
        # Test pipeline
        from src.core.pipeline import VideoProcessingPipeline
        pipeline = VideoProcessingPipeline()
        print("   ✅ Processing pipeline initialized")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Core functionality test failed: {e}")
        return False

def test_cli():
    """Test CLI functionality."""
    print("💻 Testing CLI...")
    try:
        # Test importing CLI
        from src.cli.commands import cli
        print("   ✅ CLI module imported successfully")
        
        # Test running info command
        result = subprocess.run([sys.executable, 'main.py', 'info'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("   ✅ CLI info command executed successfully")
            return True
        else:
            print(f"   ❌ CLI info command failed with code {result.returncode}")
            print(f"       Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ CLI test failed: {e}")
        return False

def test_directories():
    """Test output directory creation."""
    print("📁 Testing directory structure...")
    try:
        required_dirs = [
            "config",
            "src",
            "tests",
            "docs",
            "examples",
            "scripts"
        ]
        
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists() and dir_path.is_dir():
                print(f"   ✅ {dir_name}/ directory exists")
            else:
                print(f"   ❌ {dir_name}/ directory missing")
                missing_dirs.append(dir_name)
        
        # Test creating output directories
        output_dirs = ["downloads", "transcriptions", "summaries", "logs"]
        for dir_name in output_dirs:
            dir_path = Path(dir_name)
            dir_path.mkdir(exist_ok=True)
            if dir_path.exists():
                print(f"   ✅ {dir_name}/ output directory ready")
            else:
                print(f"   ❌ Failed to create {dir_name}/ directory")
                missing_dirs.append(dir_name)
        
        return len(missing_dirs) == 0
        
    except Exception as e:
        print(f"   ❌ Directory test failed: {e}")
        return False

def main():
    """Run all installation tests."""
    print("🚀 TikTok Video Processing System - Installation Test")
    print("=" * 60)
    
    tests = [
        ("Python Version", test_python_version),
        ("Module Imports", lambda: test_imports()[0]),
        ("FFmpeg", test_ffmpeg),
        ("Configuration", test_configuration),
        ("Core Functionality", test_core_functionality),
        ("CLI Interface", test_cli),
        ("Directory Structure", test_directories),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Edit config/config.yaml with your OpenAI API key")
        print("2. Run: python main.py process \"video_url\"")
    else:
        print("⚠️  Some tests failed. Please check the installation:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Install FFmpeg: https://ffmpeg.org/download.html")
        print("3. Check the installation guide: docs/INSTALLATION.md")
        
        # Show failed imports if any
        if "Module Imports" in [test[0] for test in tests]:
            _, failed_imports = test_imports()
            if failed_imports:
                print(f"\nMissing modules: {', '.join(failed_imports)}")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
