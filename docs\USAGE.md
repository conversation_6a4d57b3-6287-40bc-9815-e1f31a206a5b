# Usage Guide

This guide provides examples and detailed instructions for using the TikTok Video Processing System.

## Quick Start

### Basic Video Processing

Process a video with default settings:

```bash
python main.py process "https://www.bilibili.com/video/BV1234567890"
```

This will:
1. Download the video
2. Extract audio
3. Transcribe using local Whisper (base model)
4. Generate a brief summary using GPT-4

### Custom Output Directory

Specify where to save all output files:

```bash
python main.py process "https://v.douyin.com/abc123" --output-dir "./my_project"
```

### Different Summary Types

Generate different types of summaries:

```bash
# Brief summary (default)
python main.py process "video_url" --summary-type brief

# Detailed analysis
python main.py process "video_url" --summary-type detailed

# Extract keywords
python main.py process "video_url" --summary-type keywords

# Generate Q&A
python main.py process "video_url" --summary-type qa
```

### Custom Summarization Prompt

Use your own prompt for summarization:

```bash
python main.py process "video_url" --custom-prompt "Summarize this video in bullet points, focusing on technical details"
```

### Whisper Configuration

Use different Whisper models and settings:

```bash
# Use local Whisper with large model
python main.py process "video_url" --whisper-local --whisper-model large

# Use OpenAI Whisper API
python main.py process "video_url" --no-whisper-local
```

## Individual Commands

### Download Only

Download a video without processing:

```bash
python main.py download "https://www.bilibili.com/video/BV1234567890"
python main.py download "video_url" --output-dir "./downloads"
```

### Transcribe Only

Transcribe an existing audio file:

```bash
python main.py transcribe "audio.wav"
python main.py transcribe "audio.mp3" --output-dir "./transcriptions" --local --model large
```

### Summarize Only

Summarize an existing text file:

```bash
python main.py summarize "transcript.txt"
python main.py summarize "text.txt" --type detailed --output-dir "./summaries"
```

## Configuration Examples

### Using Configuration File

Edit `config/config.yaml` for persistent settings:

```yaml
# Use OpenAI API for transcription
whisper:
  use_local: false
  language: "zh"  # Force Chinese language

# Custom summarization prompts
summarization:
  prompts:
    technical: "Focus on technical aspects and provide detailed explanations"
    marketing: "Analyze from a marketing perspective, identify key selling points"
    educational: "Structure as educational content with clear learning objectives"
```

Then use custom prompts:

```bash
python main.py process "video_url" --custom-prompt technical
```

### Environment Variables

Set API keys and paths via environment variables:

```bash
export OPENAI_API_KEY="your-api-key"
export DOWNLOAD_DIR="./my_downloads"
export LOG_LEVEL="DEBUG"

python main.py process "video_url"
```

## Advanced Usage

### Batch Processing

Process multiple videos by running the command multiple times:

```bash
# Process multiple videos
python main.py process "https://www.bilibili.com/video/BV1111111111"
python main.py process "https://www.bilibili.com/video/BV2222222222"
python main.py process "https://v.douyin.com/abc123"
```

### Pipeline Processing

Use individual commands for more control:

```bash
# Step 1: Download
python main.py download "video_url" --output-dir "./temp"

# Step 2: Transcribe (assuming video was downloaded as video.mp4)
python main.py transcribe "./temp/video.wav" --output-dir "./temp"

# Step 3: Summarize
python main.py summarize "./temp/video.txt" --type detailed --output-dir "./results"
```

### Working with Long Videos

For very long videos, consider:

1. **Using API transcription** (handles chunking automatically):
```bash
python main.py process "long_video_url" --no-whisper-local
```

2. **Using smaller Whisper models** for faster processing:
```bash
python main.py process "long_video_url" --whisper-model tiny
```

3. **Processing in chunks** (manual approach):
```bash
# Download first
python main.py download "long_video_url"

# Split audio manually (using external tools)
# Then transcribe each chunk
python main.py transcribe "chunk1.wav"
python main.py transcribe "chunk2.wav"

# Combine transcripts and summarize
python main.py summarize "combined_transcript.txt"
```

## Output Structure

The system creates organized output directories:

```
output/
├── downloads/          # Downloaded videos
│   ├── video_title.mp4
│   └── video_title.wav
├── transcriptions/     # Transcription files
│   ├── video_title.txt
│   ├── video_title.srt
│   └── video_title.json
└── summaries/          # Generated summaries
    ├── summary_brief.txt
    ├── summary_detailed.txt
    └── summary_keywords.txt
```

## System Information

Check system status and configuration:

```bash
python main.py info
```

This shows:
- Component availability (FFmpeg, Whisper, GPT API)
- Current configuration settings
- Model information

## Tips and Best Practices

### Performance Optimization

1. **Use appropriate Whisper models**:
   - `tiny`: Fastest, least accurate
   - `base`: Good balance (recommended for most use cases)
   - `large`: Most accurate, slowest

2. **GPU acceleration**:
   - Install CUDA-enabled PyTorch for faster local transcription
   - Check GPU availability: `python -c "import torch; print(torch.cuda.is_available())"`

3. **API vs Local**:
   - Local Whisper: No API costs, requires more compute
   - API Whisper: Faster for single videos, costs money

### Cost Management

1. **Estimate costs** before processing:
   - Whisper API: ~$0.006 per minute of audio
   - GPT API: Varies by model and text length

2. **Use shorter prompts** for summarization to reduce token usage

3. **Consider local models** for frequent use

### Quality Optimization

1. **Audio quality matters**:
   - Ensure good audio quality in source videos
   - Use appropriate audio formats (WAV preferred for local processing)

2. **Language detection**:
   - Specify language in config if known: `language: "zh"` for Chinese
   - Let Whisper auto-detect for mixed content

3. **Prompt engineering**:
   - Be specific in custom prompts
   - Include desired output format in prompts
   - Test different prompts for best results

## Troubleshooting

### Common Issues

1. **Video download fails**:
   - Check if video is public and accessible
   - Verify URL format is correct
   - Some videos may require authentication

2. **Transcription is inaccurate**:
   - Try a larger Whisper model
   - Check audio quality
   - Specify correct language

3. **Summarization is poor**:
   - Use more specific prompts
   - Try different GPT models
   - Check if transcript quality is good

4. **Out of memory errors**:
   - Use smaller Whisper models
   - Use API instead of local models
   - Process shorter audio segments

### Getting Help

- Use verbose mode for detailed logs: `python main.py -v process "url"`
- Check log files in `logs/app.log`
- Verify configuration with `python main.py info`
