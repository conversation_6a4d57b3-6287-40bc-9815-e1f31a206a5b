"""Audio processing utilities for video transcription."""

import subprocess
import tempfile
from pathlib import Path
from typing import Optional, <PERSON>ple
from loguru import logger

import ffmpeg
import librosa
import soundfile as sf


class AudioProcessor:
    """Audio processing utilities for preparing audio for transcription."""
    
    def __init__(self):
        """Initialize audio processor."""
        self.target_sample_rate = 16000  # Whis<PERSON>'s preferred sample rate
        self.target_channels = 1  # Mono audio
    
    def extract_audio_from_video(
        self, 
        video_path: Path, 
        output_path: Optional[Path] = None,
        format: str = "wav"
    ) -> Path:
        """Extract audio from video file using ffmpeg.
        
        Args:
            video_path: Path to input video file
            output_path: Path for output audio file (auto-generated if None)
            format: Output audio format (wav, mp3, m4a)
            
        Returns:
            Path to extracted audio file
            
        Raises:
            Exception: If audio extraction fails
        """
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        if output_path is None:
            output_path = video_path.with_suffix(f".{format}")
        
        try:
            logger.info(f"Extracting audio from {video_path.name}")
            
            # Use ffmpeg to extract audio
            stream = ffmpeg.input(str(video_path))
            stream = ffmpeg.output(
                stream,
                str(output_path),
                acodec='pcm_s16le' if format == 'wav' else 'libmp3lame',
                ar=self.target_sample_rate,
                ac=self.target_channels,
                loglevel='error'
            )
            ffmpeg.run(stream, overwrite_output=True)
            
            logger.info(f"Audio extracted to {output_path.name}")
            return output_path
            
        except ffmpeg.Error as e:
            error_msg = f"FFmpeg error during audio extraction: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Failed to extract audio: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def convert_audio_format(
        self,
        input_path: Path,
        output_path: Path,
        target_format: str = "wav",
        sample_rate: Optional[int] = None,
        channels: Optional[int] = None
    ) -> Path:
        """Convert audio to different format and specifications.
        
        Args:
            input_path: Path to input audio file
            output_path: Path for output audio file
            target_format: Target audio format
            sample_rate: Target sample rate (uses default if None)
            channels: Target number of channels (uses default if None)
            
        Returns:
            Path to converted audio file
        """
        if not input_path.exists():
            raise FileNotFoundError(f"Audio file not found: {input_path}")
        
        sample_rate = sample_rate or self.target_sample_rate
        channels = channels or self.target_channels
        
        try:
            logger.info(f"Converting {input_path.name} to {target_format}")
            
            stream = ffmpeg.input(str(input_path))
            stream = ffmpeg.output(
                stream,
                str(output_path),
                acodec='pcm_s16le' if target_format == 'wav' else 'libmp3lame',
                ar=sample_rate,
                ac=channels,
                loglevel='error'
            )
            ffmpeg.run(stream, overwrite_output=True)
            
            logger.info(f"Audio converted to {output_path.name}")
            return output_path
            
        except ffmpeg.Error as e:
            error_msg = f"FFmpeg error during audio conversion: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def normalize_audio_for_whisper(self, audio_path: Path) -> Path:
        """Normalize audio file for optimal Whisper transcription.
        
        Args:
            audio_path: Path to input audio file
            
        Returns:
            Path to normalized audio file
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        try:
            logger.info(f"Normalizing audio for Whisper: {audio_path.name}")
            
            # Load audio with librosa
            audio, sr = librosa.load(str(audio_path), sr=self.target_sample_rate, mono=True)
            
            # Normalize audio to [-1, 1] range
            audio = librosa.util.normalize(audio)
            
            # Create output path
            output_path = audio_path.with_suffix('.normalized.wav')
            
            # Save normalized audio
            sf.write(str(output_path), audio, self.target_sample_rate)
            
            logger.info(f"Audio normalized: {output_path.name}")
            return output_path
            
        except Exception as e:
            error_msg = f"Failed to normalize audio: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def get_audio_info(self, audio_path: Path) -> dict:
        """Get audio file information.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Dictionary with audio information
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        try:
            # Use ffprobe to get audio info
            probe = ffmpeg.probe(str(audio_path))
            audio_stream = next(
                (stream for stream in probe['streams'] if stream['codec_type'] == 'audio'),
                None
            )
            
            if not audio_stream:
                raise Exception("No audio stream found in file")
            
            info = {
                'duration': float(probe['format']['duration']),
                'sample_rate': int(audio_stream['sample_rate']),
                'channels': int(audio_stream['channels']),
                'codec': audio_stream['codec_name'],
                'bitrate': int(probe['format'].get('bit_rate', 0)),
                'size': int(probe['format']['size']),
            }
            
            return info
            
        except Exception as e:
            error_msg = f"Failed to get audio info: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def split_audio_by_duration(
        self,
        audio_path: Path,
        max_duration: int = 600,  # 10 minutes
        output_dir: Optional[Path] = None
    ) -> list[Path]:
        """Split long audio file into smaller chunks for processing.
        
        Args:
            audio_path: Path to input audio file
            max_duration: Maximum duration per chunk in seconds
            output_dir: Directory for output chunks (same as input if None)
            
        Returns:
            List of paths to audio chunks
        """
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        if output_dir is None:
            output_dir = audio_path.parent
        
        try:
            # Get audio duration
            info = self.get_audio_info(audio_path)
            total_duration = info['duration']
            
            if total_duration <= max_duration:
                # No need to split
                return [audio_path]
            
            logger.info(f"Splitting audio into chunks of {max_duration}s")
            
            chunks = []
            chunk_count = int(total_duration / max_duration) + 1
            
            for i in range(chunk_count):
                start_time = i * max_duration
                chunk_duration = min(max_duration, total_duration - start_time)
                
                if chunk_duration <= 0:
                    break
                
                chunk_path = output_dir / f"{audio_path.stem}_chunk_{i+1:03d}.wav"
                
                # Extract chunk using ffmpeg
                stream = ffmpeg.input(str(audio_path), ss=start_time, t=chunk_duration)
                stream = ffmpeg.output(
                    stream,
                    str(chunk_path),
                    acodec='pcm_s16le',
                    loglevel='error'
                )
                ffmpeg.run(stream, overwrite_output=True)
                
                chunks.append(chunk_path)
                logger.info(f"Created chunk: {chunk_path.name}")
            
            return chunks
            
        except Exception as e:
            error_msg = f"Failed to split audio: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def check_ffmpeg_availability(self) -> bool:
        """Check if ffmpeg is available on the system.
        
        Returns:
            True if ffmpeg is available, False otherwise
        """
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
