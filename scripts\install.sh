#!/bin/bash

# TikTok Video Processing System Installation Script
# This script automates the installation process

set -e  # Exit on any error

echo "🚀 TikTok Video Processing System Installation"
echo "=============================================="

# Check Python version
echo "📋 Checking Python version..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8 or higher is required. Found: $python_version"
    exit 1
fi
echo "✅ Python version: $python_version"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt
echo "✅ Python dependencies installed"

# Check FFmpeg installation
echo "🎵 Checking FFmpeg installation..."
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg is installed: $(ffmpeg -version | head -n1)"
else
    echo "⚠️  FFmpeg not found. Please install FFmpeg:"
    echo "   - Ubuntu/Debian: sudo apt install ffmpeg"
    echo "   - macOS: brew install ffmpeg"
    echo "   - Windows: Download from https://ffmpeg.org/download.html"
fi

# Create configuration file
echo "⚙️  Setting up configuration..."
if [ ! -f "config/config.yaml" ]; then
    mkdir -p config
    cp config/config.example.yaml config/config.yaml
    echo "✅ Configuration file created from example"
    echo "📝 Please edit config/config.yaml with your API keys"
else
    echo "✅ Configuration file already exists"
fi

# Create .env file
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ Environment file created from example"
    echo "📝 Please edit .env with your API keys"
else
    echo "✅ Environment file already exists"
fi

# Create output directories
echo "📁 Creating output directories..."
mkdir -p downloads transcriptions summaries logs
echo "✅ Output directories created"

# Test installation
echo "🧪 Testing installation..."
python main.py info > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Installation test passed"
else
    echo "⚠️  Installation test failed. Please check the logs."
fi

echo ""
echo "🎉 Installation completed!"
echo ""
echo "Next steps:"
echo "1. Edit config/config.yaml with your OpenAI API key"
echo "2. Run: python main.py info (to verify setup)"
echo "3. Run: python main.py process \"video_url\" (to process a video)"
echo ""
echo "For more information, see docs/USAGE.md"
