@echo off
REM TikTok Video Processing System Installation Script for Windows
REM This script automates the installation process

echo 🚀 TikTok Video Processing System Installation
echo ==============================================

REM Check Python version
echo 📋 Checking Python version...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python version: %python_version%

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    echo ✅ Virtual environment created
) else (
    echo ✅ Virtual environment already exists
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo ⬆️  Upgrading pip...
python -m pip install --upgrade pip

REM Install Python dependencies
echo 📦 Installing Python dependencies...
pip install -r requirements.txt
echo ✅ Python dependencies installed

REM Check FFmpeg installation
echo 🎵 Checking FFmpeg installation...
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  FFmpeg not found. Please install FFmpeg:
    echo    Download from https://ffmpeg.org/download.html
    echo    Add FFmpeg to your system PATH
) else (
    echo ✅ FFmpeg is installed
)

REM Create configuration file
echo ⚙️  Setting up configuration...
if not exist "config\config.yaml" (
    if not exist "config" mkdir config
    copy config\config.example.yaml config\config.yaml >nul
    echo ✅ Configuration file created from example
    echo 📝 Please edit config\config.yaml with your API keys
) else (
    echo ✅ Configuration file already exists
)

REM Create .env file
if not exist ".env" (
    copy .env.example .env >nul
    echo ✅ Environment file created from example
    echo 📝 Please edit .env with your API keys
) else (
    echo ✅ Environment file already exists
)

REM Create output directories
echo 📁 Creating output directories...
if not exist "downloads" mkdir downloads
if not exist "transcriptions" mkdir transcriptions
if not exist "summaries" mkdir summaries
if not exist "logs" mkdir logs
echo ✅ Output directories created

REM Test installation
echo 🧪 Testing installation...
python main.py info >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Installation test failed. Please check the logs.
) else (
    echo ✅ Installation test passed
)

echo.
echo 🎉 Installation completed!
echo.
echo Next steps:
echo 1. Edit config\config.yaml with your OpenAI API key
echo 2. Run: python main.py info (to verify setup)
echo 3. Run: python main.py process "video_url" (to process a video)
echo.
echo For more information, see docs\USAGE.md
echo.
pause
