#!/usr/bin/env python3
"""
Example usage of the TikTok Video Processing System API.
This script demonstrates how to use the system programmatically.
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.pipeline import VideoProcessingPipeline
from src.core.config import get_config
from src.downloaders.manager import DownloadManager
from src.transcription.manager import TranscriptionManager
from src.summarization.manager import SummarizationManager
from src.summarization.base import SummaryType
from src.utils.audio import AudioProcessor


def example_full_pipeline():
    """Example: Complete video processing pipeline."""
    print("🎬 Example: Full Video Processing Pipeline")
    print("=" * 50)
    
    # Initialize pipeline
    pipeline = VideoProcessingPipeline("./example_output")
    
    # Example video URL (replace with actual URL)
    video_url = "https://www.bilibili.com/video/BV1234567890"
    
    # Process video with custom settings
    result = pipeline.process_video(
        url=video_url,
        summary_type=SummaryType.DETAILED,
        use_local_whisper=True,
        keep_video=False
    )
    
    if result.success:
        print("✅ Processing completed successfully!")
        print(f"📹 Video: {result.video_info['title'] if result.video_info else 'Unknown'}")
        print(f"📝 Transcription: {len(result.transcription_text)} characters")
        print(f"📄 Summary: {len(result.summary_text)} characters")
        print(f"⏱️  Total time: {result.processing_time:.2f}s")
        print(f"📁 Files saved to: {pipeline.base_output_dir}")
    else:
        print(f"❌ Processing failed: {result.error_message}")


def example_individual_steps():
    """Example: Using individual components separately."""
    print("\n🔧 Example: Individual Processing Steps")
    print("=" * 50)
    
    # Step 1: Download video
    print("📥 Step 1: Downloading video...")
    download_manager = DownloadManager("./example_downloads")
    
    video_url = "https://www.bilibili.com/video/BV1234567890"
    download_result = download_manager.download(video_url, extract_audio=True)
    
    if not download_result.success:
        print(f"❌ Download failed: {download_result.error_message}")
        return
    
    print(f"✅ Downloaded: {download_result.video_path}")
    print(f"🎵 Audio: {download_result.audio_path}")
    
    # Step 2: Transcribe audio
    print("\n🎙️ Step 2: Transcribing audio...")
    transcription_manager = TranscriptionManager()
    
    transcription_result = transcription_manager.transcribe(
        download_result.audio_path,
        use_local=True,
        output_dir=Path("./example_transcriptions")
    )
    
    print(f"✅ Transcription completed")
    print(f"📝 Text length: {len(transcription_result.text)} characters")
    print(f"🌍 Language: {transcription_result.language}")
    
    # Step 3: Generate summary
    print("\n📄 Step 3: Generating summary...")
    summarization_manager = SummarizationManager()
    
    summary_result = summarization_manager.summarize(
        transcription_result.text,
        summary_type=SummaryType.BRIEF,
        output_file=Path("./example_summaries/summary.txt")
    )
    
    print(f"✅ Summary generated")
    print(f"📊 Compression ratio: {summary_result.compression_ratio:.2%}")
    print(f"📄 Summary preview: {summary_result.summary[:200]}...")


def example_multiple_summaries():
    """Example: Generate multiple types of summaries."""
    print("\n📚 Example: Multiple Summary Types")
    print("=" * 50)
    
    # Sample text (in practice, this would come from transcription)
    sample_text = """
    This is a sample video transcript about artificial intelligence and machine learning.
    The speaker discusses the latest developments in neural networks, deep learning algorithms,
    and their applications in various industries including healthcare, finance, and autonomous vehicles.
    Key topics covered include transformer architectures, attention mechanisms, and the future
    of AI research. The presentation also touches on ethical considerations and the importance
    of responsible AI development.
    """
    
    summarization_manager = SummarizationManager()
    
    # Generate different types of summaries
    summary_types = [SummaryType.BRIEF, SummaryType.DETAILED, SummaryType.KEYWORDS, SummaryType.QA]
    
    results = summarization_manager.generate_multiple_summaries(
        sample_text,
        summary_types,
        output_dir=Path("./example_summaries"),
        base_filename="ai_video"
    )
    
    for summary_type, result in results.items():
        print(f"\n📋 {summary_type.value.title()} Summary:")
        print(f"   Length: {len(result.summary)} characters")
        print(f"   Preview: {result.summary[:150]}...")


def example_audio_processing():
    """Example: Audio processing utilities."""
    print("\n🎵 Example: Audio Processing")
    print("=" * 50)
    
    audio_processor = AudioProcessor()
    
    # Check FFmpeg availability
    if audio_processor.check_ffmpeg_availability():
        print("✅ FFmpeg is available")
    else:
        print("❌ FFmpeg not found - audio processing will be limited")
        return
    
    # Example: Extract audio from video (if you have a video file)
    video_file = Path("example_video.mp4")
    if video_file.exists():
        print(f"🎬 Extracting audio from: {video_file}")
        
        audio_file = audio_processor.extract_audio_from_video(
            video_file,
            output_path=Path("extracted_audio.wav")
        )
        
        # Get audio information
        audio_info = audio_processor.get_audio_info(audio_file)
        print(f"📊 Audio info:")
        print(f"   Duration: {audio_info['duration']:.2f} seconds")
        print(f"   Sample rate: {audio_info['sample_rate']} Hz")
        print(f"   Channels: {audio_info['channels']}")
        print(f"   Codec: {audio_info['codec']}")
    else:
        print("ℹ️  No example video file found, skipping audio extraction demo")


def example_configuration():
    """Example: Working with configuration."""
    print("\n⚙️ Example: Configuration Management")
    print("=" * 50)
    
    config = get_config()
    
    print("📋 Current Configuration:")
    print(f"   OpenAI Model: {config.openai.model}")
    print(f"   Whisper Mode: {'Local' if config.whisper.use_local else 'API'}")
    print(f"   Whisper Model: {config.whisper.model_size}")
    print(f"   Download Dir: {config.download.output_dir}")
    print(f"   Transcription Dir: {config.transcription.output_dir}")
    print(f"   Summary Dir: {config.summarization.output_dir}")
    
    # Example: Custom prompt usage
    custom_prompts = config.summarization.prompts
    if custom_prompts:
        print(f"\n📝 Available Custom Prompts:")
        for name, prompt in custom_prompts.items():
            print(f"   {name}: {prompt[:100]}...")


def example_error_handling():
    """Example: Error handling and validation."""
    print("\n🛡️ Example: Error Handling")
    print("=" * 50)
    
    from src.utils.validation import validate_video_url, validate_api_key
    from src.utils.error_handler import ErrorHandler
    from src.utils.exceptions import UnsupportedPlatformError
    
    error_handler = ErrorHandler(verbose=True)
    
    # Example: URL validation
    test_urls = [
        "https://www.bilibili.com/video/BV1234567890",  # Valid
        "https://www.youtube.com/watch?v=abc123",       # Invalid (unsupported)
        "not-a-url",                                    # Invalid format
    ]
    
    print("🔍 URL Validation:")
    for url in test_urls:
        is_valid = validate_video_url(url)
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"   {url}: {status}")
    
    # Example: Error handling
    print("\n🚨 Error Handling Demo:")
    try:
        # Simulate an unsupported platform error
        raise UnsupportedPlatformError("https://unsupported-platform.com/video/123")
    except Exception as e:
        error_info = error_handler.handle_error(e, "URL validation")
        print(f"   Error: {error_info['user_message']}")
        print(f"   Suggestions:")
        for suggestion in error_info['suggestions']:
            print(f"     • {suggestion}")


def main():
    """Run all examples."""
    print("🚀 TikTok Video Processing System - API Examples")
    print("=" * 60)
    
    try:
        # Configuration example
        example_configuration()
        
        # Error handling example
        example_error_handling()
        
        # Audio processing example
        example_audio_processing()
        
        # Multiple summaries example (works without external dependencies)
        example_multiple_summaries()
        
        # Note: Commented out examples that require actual video URLs and API keys
        # Uncomment and modify these when you have proper configuration:
        
        # example_individual_steps()
        # example_full_pipeline()
        
        print("\n🎉 Examples completed!")
        print("\nℹ️  Note: Some examples are commented out and require:")
        print("   • Valid video URLs")
        print("   • OpenAI API key configuration")
        print("   • Proper setup of config/config.yaml")
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
