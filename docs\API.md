# API Documentation

This document describes the internal API and classes of the TikTok Video Processing System.

## Core Components

### Configuration Management

#### `Config` Class

Main configuration container with all settings.

```python
from src.core.config import get_config

config = get_config()
print(config.openai.api_key)
print(config.whisper.model_size)
```

#### `ConfigManager` Class

Manages loading and validation of configuration.

```python
from src.core.config import ConfigManager

manager = ConfigManager("custom_config.yaml")
config = manager.load_config()
```

### Video Download

#### `DownloadManager` Class

Coordinates video downloads across different platforms.

```python
from src.downloaders.manager import DownloadManager

manager = DownloadManager("./downloads")
result = manager.download("https://www.bilibili.com/video/BV123")

if result.success:
    print(f"Downloaded: {result.video_path}")
    print(f"Audio: {result.audio_path}")
else:
    print(f"Error: {result.error_message}")
```

#### `DownloadResult` Class

Contains the result of a download operation.

```python
@dataclass
class DownloadResult:
    success: bool
    video_path: Optional[Path] = None
    audio_path: Optional[Path] = None
    video_info: Optional[VideoInfo] = None
    error_message: Optional[str] = None
```

### Transcription

#### `TranscriptionManager` Class

Manages transcription using local or API Whisper models.

```python
from src.transcription.manager import TranscriptionManager

manager = TranscriptionManager()
result = manager.transcribe(
    audio_path=Path("audio.wav"),
    use_local=True,
    output_dir=Path("./transcriptions")
)

print(result.text)
print(f"Language: {result.language}")
```

#### `TranscriptionResult` Class

Contains transcription output with segments and metadata.

```python
@dataclass
class TranscriptionResult:
    text: str
    segments: List[TranscriptionSegment]
    language: Optional[str] = None
    confidence: Optional[float] = None
    processing_time: Optional[float] = None
```

### Summarization

#### `SummarizationManager` Class

Manages AI-powered text summarization using GPT models.

```python
from src.summarization.manager import SummarizationManager
from src.summarization.base import SummaryType

manager = SummarizationManager()
result = manager.summarize(
    text="Long text to summarize...",
    summary_type=SummaryType.BRIEF,
    output_file=Path("summary.txt")
)

print(result.summary)
print(f"Compression: {result.compression_ratio:.2%}")
```

#### `SummaryResult` Class

Contains summarization output and metadata.

```python
@dataclass
class SummaryResult:
    summary: str
    summary_type: SummaryType
    original_length: int
    summary_length: int
    compression_ratio: float
    processing_time: Optional[float] = None
    model_used: Optional[str] = None
```

### Processing Pipeline

#### `VideoProcessingPipeline` Class

Main pipeline that coordinates all processing steps.

```python
from src.core.pipeline import VideoProcessingPipeline
from src.summarization.base import SummaryType

pipeline = VideoProcessingPipeline("./output")
result = pipeline.process_video(
    url="https://www.bilibili.com/video/BV123",
    summary_type=SummaryType.DETAILED,
    use_local_whisper=True,
    keep_video=False
)

if result.success:
    print(f"Video: {result.video_info['title']}")
    print(f"Transcription: {len(result.transcription_text)} chars")
    print(f"Summary: {result.summary_file}")
else:
    print(f"Error: {result.error_message}")
```

## Utility Classes

### Audio Processing

#### `AudioProcessor` Class

Handles audio extraction and format conversion.

```python
from src.utils.audio import AudioProcessor

processor = AudioProcessor()

# Extract audio from video
audio_path = processor.extract_audio_from_video(
    video_path=Path("video.mp4"),
    output_path=Path("audio.wav")
)

# Get audio information
info = processor.get_audio_info(audio_path)
print(f"Duration: {info['duration']}s")
print(f"Sample rate: {info['sample_rate']}Hz")
```

### Error Handling

#### `ErrorHandler` Class

Provides user-friendly error messages and suggestions.

```python
from src.utils.error_handler import ErrorHandler

handler = ErrorHandler(verbose=True)

try:
    # Some operation that might fail
    pass
except Exception as e:
    error_info = handler.handle_error(e, "video processing")
    print(error_info['user_message'])
    for suggestion in error_info['suggestions']:
        print(f"- {suggestion}")
```

### Validation

#### Validation Functions

Various validation utilities for inputs and configuration.

```python
from src.utils.validation import (
    validate_video_url,
    validate_api_key,
    validate_whisper_model,
    sanitize_filename
)

# Validate inputs
assert validate_video_url("https://www.bilibili.com/video/BV123")
assert validate_api_key("sk-1234567890abcdef...")
assert validate_whisper_model("base")

# Sanitize filename
safe_name = sanitize_filename("Video: Title with/invalid\\chars.mp4")
print(safe_name)  # "Video_ Title with_invalid_chars.mp4"
```

## Custom Exceptions

The system defines custom exceptions for better error handling:

```python
from src.utils.exceptions import (
    VideoProcessingError,
    DownloadError,
    UnsupportedPlatformError,
    TranscriptionError,
    SummarizationError,
    APIError,
    ConfigurationError
)

try:
    # Some operation
    pass
except UnsupportedPlatformError as e:
    print(f"Platform not supported: {e.platform}")
except DownloadError as e:
    print(f"Download failed for {e.url}: {e}")
except TranscriptionError as e:
    print(f"Transcription failed: {e}")
```

## Extending the System

### Adding New Downloaders

To add support for a new platform:

1. Create a new downloader class inheriting from `BaseDownloader`
2. Implement required methods: `can_handle()`, `get_video_info()`, `download()`
3. Register with `DownloadManager`

```python
from src.downloaders.base import BaseDownloader, DownloadResult

class CustomDownloader(BaseDownloader):
    def can_handle(self, url: str) -> bool:
        return "custom-platform.com" in url
    
    def get_video_info(self, url: str) -> VideoInfo:
        # Implementation
        pass
    
    def download(self, url: str, extract_audio: bool = True) -> DownloadResult:
        # Implementation
        pass

# Register with manager
manager = DownloadManager()
manager.add_downloader(CustomDownloader())
```

### Adding New Transcription Services

To add a new transcription service:

1. Create a class inheriting from `BaseTranscriber`
2. Implement `transcribe()` and `is_available()` methods
3. Update `TranscriptionManager` to use the new service

```python
from src.transcription.base import BaseTranscriber, TranscriptionResult

class CustomTranscriber(BaseTranscriber):
    def transcribe(self, audio_path: Path) -> TranscriptionResult:
        # Implementation
        pass
    
    def is_available(self) -> bool:
        # Check if service is available
        return True
```

### Adding New Summarization Services

To add a new summarization service:

1. Create a class inheriting from `BaseSummarizer`
2. Implement `summarize()` and `is_available()` methods
3. Update `SummarizationManager` to use the new service

```python
from src.summarization.base import BaseSummarizer, SummaryResult

class CustomSummarizer(BaseSummarizer):
    def summarize(self, text: str, **kwargs) -> SummaryResult:
        # Implementation
        pass
    
    def is_available(self) -> bool:
        # Check if service is available
        return True
```

## Configuration Schema

The configuration file follows this schema:

```yaml
openai:
  api_key: string (required)
  model: string (default: "gpt-4")
  temperature: float (default: 0.7)
  max_tokens: int (default: 1000)

whisper:
  use_local: bool (default: true)
  model_size: string (default: "base")
  language: string (optional)
  device: string (default: "auto")

download:
  output_dir: string (default: "./downloads")
  keep_video: bool (default: true)
  quality: string (default: "best")

transcription:
  output_dir: string (default: "./transcriptions")
  formats: list[string] (default: ["txt", "srt"])
  include_timestamps: bool (default: true)

summarization:
  output_dir: string (default: "./summaries")
  default_prompt: string
  prompts: dict[string, string]

logging:
  level: string (default: "INFO")
  file: string (default: "./logs/app.log")
  format: string

platforms:
  bilibili:
    cookies: string (optional)
    user_agent: string (optional)
  douyin:
    cookies: string (optional)
    user_agent: string (optional)
```
